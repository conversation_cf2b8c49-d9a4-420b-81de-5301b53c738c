'use client'
import React, { useState } from 'react'
import { PNRCreation } from '@/components/pnr/PNRCreation'
import PNRDetails from '@/components/pnr/PNRDetails'

export default function PNRPage() {
  const [pnrDetails, setPNRDetails] = useState(null)
  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-[#E81932] dark:text-white mb-4">PNR Creation</h1>
        <p className="text-gray-600 dark:text-gray-300">Create and manage passenger reservations</p>
      </div>
      {!pnrDetails ? (
        <PNRCreation onPNRCreated={setPNRDetails} />
      ) : (
        <PNRDetails pnr={pnrDetails} onNewPNR={() => setPNRDetails(null)} />
      )}
    </div>
  )
}


