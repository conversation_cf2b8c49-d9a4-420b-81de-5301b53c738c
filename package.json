{"name": "pnr-component-refactor", "version": "1.0.0", "description": "Refactored PNR components with comprehensive testing", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "lucide-react": "^0.294.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "sonner": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-label": "^2.0.2", "cmdk": "^0.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.2.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "jest": "^29.7.0", "@jest/types": "^29.6.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}}