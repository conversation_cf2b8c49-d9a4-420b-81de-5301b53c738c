'use client';
import {
    <PERSON>,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plane, CheckCircle2, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import keyGenerator from '@/lib/client/keyGenerator';
import useUserApi from '@/hooks/api/useUserApi';
import React, { useState } from 'react';
import FlightInfo from '@/components/shared/flight/FlightInfo';
import { useMatomo } from '@jonkoops/matomo-tracker-react';
import { SAVED } from '@/lib/client/constants/matomoConstants';
import PassengerInfo from '@/components/shared/flight/PassengertInfo';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog';
 
export function PNRDetails({ pnr, onNewPNR }) {
    const { saveBooking } = useUserApi();
    const { trackEvent } = useMatomo();
 
    const [isSavePnrDisabled, setIsSavePnrDisabled] = useState(false);
 
    const onSavePNR = () => {
        trackEvent(SAVED.CLICK_PNR_SAVED_BUTTON);
        saveBooking(pnr).then(() => {
            trackEvent(SAVED.PNR_SAVED_SUCCESS);
            toast.info(
                'PNR has been successfully saved. You can access it from the Saved Page.'
            );
            setIsSavePnrDisabled(true);
        });
    };
 
    return (
        <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle className="flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5 text-green-500" />
                        PNR Created Successfully
                    </CardTitle>
                    <CardDescription className="mt-3">
                        Pnr Number: {pnr.pnrNumber}
                    </CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={onNewPNR}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    New PNR
                </Button>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid gap-6 md:grid-cols-1">
                    <div className="space-y-4">
                        <div className="flex justify-between">
                            <h3 className="font-medium flex items-center gap-2 ">
                                <Plane className="h-4 w-4 text-[#E81932]" />
                                Booking Details
                            </h3>
                            <Dialog>
                                <DialogTrigger asChild>
                                    <Button variant="outline">
                                        Show TROYA Text
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="w-[60vw] max-h-[70vh]">
                                    <DialogHeader>
                                        <DialogTitle>TROYA Text</DialogTitle>
                                    </DialogHeader>
                                    <div className="whitespace-pre-wrap break-words overflow-y-auto max-h-[55vh] p-2">
                                        {pnr?.troyaText}
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>
                        <div className="bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-800 dark:to-gray-900 p-6 rounded-lg shadow-lg">
                            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                                PNR Details
                            </h3>
                            <div className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">PNR:</span>
                                    <span className="font-bold text-gray-800 dark:text-gray-200">
                                        {pnr.pnrNumber}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">
                                        Surname:
                                    </span>
                                    <span className="font-bold text-gray-800 dark:text-gray-200">
                                        {pnr.surname}
                                    </span>
                                </div>
                                {pnr.flights.map((flight) => (
                                    <FlightInfo
                                        key={keyGenerator()}
                                        flight={flight}
                                    />
                                ))}
                                <PassengerInfo passengers={pnr.passengers} />
                            </div>
                        </div>
                    </div>
                </div>
 
                <div className="flex justify-center pt-4">
                    <Button
                        className="w-full max-w-sm"
                        onClick={onSavePNR}
                        disabled={isSavePnrDisabled}
                    >
                        Save PNR
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
