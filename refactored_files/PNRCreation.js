'use client';
 
import React, { useContext, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle
} from '@/components/ui/card';
import { FlightSegment } from '@/components/flight-search/FlightSegment';
import { PassengerSelect } from '@/components/flight-search/PassengerSelect';
import { PassengerDetails } from '@/components/pnr/PassengerDetails';
import { ContactDetails } from '@/components/pnr/ContactDetails';
import { BookingTypeCard } from '@/components/pnr/BookingTypeCard';
import { PaymentMethodSelect } from '@/components/payment/PaymentMethodSelect';
import PaymentCardInformation from '@/components/payment/PaymentCardInformation';
import { dayDifference, addDays } from '@/utils/dateUtils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    awardTicketFareTypes,
    defaultChannel,
    fareTypes
} from '@/lib/client/constants';
import { PASSENGER_TYPES } from '@/lib/client/constants/passengers';
import {
    AlertCircle,
    ArrowRightLeft,
    CreditCard,
    Loader2,
    PlusIcon,
    Phone
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
    isMsReqPassengerType,
    validateMilesBookingPassengers
} from '@/lib/client/validations/passenger';
import { validateCreditCard } from '@/lib/client/utils/payment';
import { LogContext } from '@/context/log/LogContext';
import { ChannelSelect } from '@/components/pnr/ChannelSelect';
import { AdditionalServicesCard } from '@/components/additionalServices/AdditionalServicesCard';
import { cn } from '@/lib/client/utils';
import useDataApi from '@/hooks/api/useDataApi';
import SaveTransactionToggle from '@/components/transaction/SaveTransactionToggle';
import { useTransactionManager } from '@/hooks/useTransactionManager';
import keyGenerator from '@/lib/client/keyGenerator';
import { useMatomo } from '@jonkoops/matomo-tracker-react';
import { PNR, SAVED } from '@/lib/client/constants/matomoConstants';
import { toast } from 'sonner';
import useUserApi from '@/hooks/api/useUserApi';
import { validateCompleteContact } from '@/lib/client/validations/contact';
 
const defaultFlight = {
    from: '',
    to: '',
    date: new Date(),
    flightHour: undefined,
    useFlightHour: false,
    stopover: '0',
    fareType: 'ANY'
};
 
export function PNRCreation({ onPNRCreated, initialData = {} }) {
    const { trackEvent } = useMatomo();
    const { getTestCreditCards, saveTestCreditCard } = useUserApi();
    const [step, setStep] = useState(1);
    const [tripType, setTripType] = useState(initialData.type || 'oneway');
    const [isCreatingPNR, setIsCreatingPNR] = useState(false);
    const [pnrType, setPnrType] = useState('CASH_TICKET');
    const [fareTypeOptions, setFareTypeOptions] = useState([]);
    const [serviceRequests, setServiceRequests] = useState({});
    const [showPassengerDetails, setShowPassengerDetails] = useState(false);
    const [showContactDetails, setShowContactDetails] = useState(false);
    const [contact, setContact] = useState(null);
    const [channel, setChannel] = useState(defaultChannel);
    const [outboundFlight, setOutboundFlight] = useState({
        ...defaultFlight,
        from: initialData.from?.toUpperCase() || '',
        to: initialData.to?.toUpperCase() || ''
    });
    const [returnFlight, setReturnFlight] = useState({
        ...defaultFlight,
        from: initialData.to?.toUpperCase() || '',
        to: initialData.from?.toUpperCase() || ''
    });
    const [multiCityFlights, setMultiCityFlights] = useState([
        {
            ...defaultFlight,
            date: null,
            from: initialData.from?.toUpperCase() || '',
            to: initialData.to?.toUpperCase() || ''
        },
        { ...defaultFlight, date: null }
    ]);
 
    const [newCardView, setNewCardView] = useState(false);
 
    const [savedCards, setSavedCards] = useState([]);
 
    const [selectedCard, setSelectedCard] = useState(null);
 
    useEffect(() => {
        getTestCreditCards().then((creditCards) => {
            if (creditCards.length > 0) {
                setNewCardView(false);
                setSelectedCard(creditCards[0]);
                setSavedCards(creditCards);
            }
        });
    }, []);
 
    const [saveForFuture, setSaveForFuture] = useState(true);
 
    // Added state for saving transaction
    const [saveTransaction, setSaveTransaction] = useState(false);
    const [transactionName, setTransactionName] = useState('');
 
    const { openLogBottomSheet } = useContext(LogContext);
    const { createNewTransaction } = useTransactionManager();
 
    useEffect(() => {
        let fareTypeState = fareTypes;
        switch (pnrType) {
            case 'AWARD_TICKET_MILES_TAX':
            case 'AWARD_TICKET_CASH_TAX':
                fareTypeState = awardTicketFareTypes;
                break;
        }
        setFareTypeOptions(fareTypeState);
    }, [pnrType]);
 
    // Parse passenger data from URL
    useEffect(() => {
        if (initialData.passenger) {
            try {
                const passengerData = Array.isArray(initialData.passenger)
                    ? initialData.passenger.map((p) => JSON.parse(p))
                    : [JSON.parse(initialData.passenger)];
 
                // Initialize passenger counts based on PASSENGER_TYPES
                const passengerCounts = Object.keys(PASSENGER_TYPES).reduce(
                    (acc, type) => {
                        acc[type] =
                            passengerData.find((p) => p.type === type)?.count ||
                            0;
                        return acc;
                    },
                    {}
                );
 
                setPassengers(passengerCounts);
 
                // Initialize passenger info array for all passenger types
                const newPassengerInfo = Object.entries(
                    passengerCounts
                ).flatMap(([type, count]) =>
                    Array(count)
                        .fill()
                        .map(() => ({
                            key: keyGenerator(),
                            type,
                            info: {
                                title: '',
                                firstName: '',
                                lastName: '',
                                dob: '',
                                passport: ''
                            }
                        }))
                );
                setPassengerInfo(newPassengerInfo);
            } catch (error) {
                console.error('Error parsing passenger data:', error);
            }
        }
    }, [initialData.passenger]);
 
    const initialPassengerCounts = Object.keys(PASSENGER_TYPES).reduce(
        (acc, type) => {
            acc[type] = type === 'ADULT' ? 1 : 0; // Set adult count to 1, others to 0
            return acc;
        },
        {}
    ); // Initialize with an empty object
 
    const [passengers, setPassengers] = useState(initialPassengerCounts);
 
    const [passengerInfo, setPassengerInfo] = useState([
        {
            key: keyGenerator(),
            type: 'ADULT',
            info: {
                title: '',
                firstName: '',
                lastName: '',
                dob: '',
                passport: ''
            }
        }
    ]);
 
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
 
    const { createPNR } = useDataApi();
 
    const addFlight = () => {
        if (multiCityFlights.length < 5) {
            setMultiCityFlights([
                ...multiCityFlights,
                { ...defaultFlight, date: null }
            ]);
        }
    };
 
    const removeFlight = (index) => {
        if (multiCityFlights.length > 2) {
            setMultiCityFlights(multiCityFlights.filter((_, i) => i !== index));
        }
    };
 
    const updateMultiCityFlight = (index, field, value) => {
        setMultiCityFlights((prev) =>
            prev.map((flight, i) =>
                i === index ? { ...flight, [field]: value } : flight
            )
        );
    };
 
    const swapFlights = () => {
        setOutboundFlight((prevOutbound) => {
            const updatedOutbound = {
                ...returnFlight,
                from: returnFlight.from,
                to: returnFlight.to,
                date: prevOutbound.date,
                stopover: returnFlight.stopover,
                fareType: returnFlight.fareType
            };
            setReturnFlight({
                ...prevOutbound,
                from: prevOutbound.from,
                to: prevOutbound.to,
                date: returnFlight.date,
                stopover: prevOutbound.stopover,
                fareType: prevOutbound.fareType
            });
            return updatedOutbound;
        });
    };
 
    const getFlights = () => {
        switch (tripType) {
            case 'round':
                return [outboundFlight, returnFlight];
            case 'multi':
                return multiCityFlights;
            default:
                return [outboundFlight];
        }
    };
 
    // Function to format day to flight info
    const formatDayToFlight = (flight) => {
        if (flight.useFlightHour) {
            return 'Specific Hour';
        }
        if (!flight.date) {
            return 'Flexible';
        }
        const days = dayDifference(new Date(), flight.date);
        return `D${days >= 0 ? '+' : ''}${days}`;
    };
 
    // Update transaction name when relevant data changes
    useEffect(() => {
        if (validateFlightDetails()) {
            setTransactionName(generateDefaultTransactionName());
        }
    }, [tripType, outboundFlight, returnFlight, multiCityFlights]);
 
    const validateContactDetails = () => {
        return validateCompleteContact(contact);
    };
 
    const generateDefaultTransactionName = () => {
        const flights = getFlights();
        let routeType = '';
 
        switch (tripType) {
            case 'round':
                routeType = 'Round Trip';
                break;
            case 'multi':
                routeType = 'Multi-City';
                break;
            default:
                routeType = 'One Way';
        }
 
 
        // Get first and last flight for name
        const firstFlight = flights[0];
        const lastFlight = flights[flights.length - 1];
 
        // Create base name from route type and locations
        let name = `${routeType}: ${firstFlight.from}-${lastFlight.to}`;
 
        // Add day to flight info if available
        if (firstFlight.date || firstFlight.useFlightHour) {
            name += ` (${formatDayToFlight(firstFlight)}`;
 
            // Add return flight info for round trips
            if (
                tripType === 'round' &&
                (lastFlight.date || lastFlight.useFlightHour)
            ) {
                name += ` - ${formatDayToFlight(lastFlight)}`;
            }
 
            name += ')';
        }
 
        return name;
    };
 
    const handleCreatePNR = async () => {
        trackEvent(PNR.CLICK_CREATE_PNR_BUTTON);
        const transaction = {
            type: 'PNR',
            name: transactionName,
            isSaved: saveTransaction
        };
 
        try {
            setIsCreatingPNR(true);
 
            if (saveForFuture && newCardView) {
                trackEvent(SAVED.CHECK_SAVE_FOR_FUTURE_PAYMENT);
                saveTestCreditCard(selectedCard).then(() => {
                    setSavedCards([...savedCards, selectedCard]);
                    setNewCardView(false);
                    toast.info('Card is saved for future use.');
                });
            }
 
            const flights = getFlights().map((flight) => ({
                origin: flight.from.toUpperCase(),
                destination: flight.to.toUpperCase(),
                ...(flight.useFlightHour
                    ? { flightHour: flight.flightHour }
                    : { dayToFlight: dayDifference(new Date(), flight.date) }),
                fareType: flight.fareType.toUpperCase(),
                stopover: parseInt(flight.stopover)
            }));
 
            const getRoutingType = (type) => {
                switch (type) {
                    case 'round':
                        return 'ROUNDTRIP';
                    case 'multi':
                        return 'MULTICITY';
                    default:
                        return 'ONEWAY';
                }
            };
 
            const isAwardTicket = pnrType.startsWith('AWARD_TICKET');
 
            const request = {
                pnrType: isAwardTicket ? 'AWARD_TICKET' : pnrType,
                routingType: getRoutingType(tripType),
                flights,
                passengers: (() => {
                    const bundledPassengers = [];
                    let typeCount = {};
 
                    const addTypeQuantity = () => {
                        for (const [key, value] of Object.entries(typeCount)) {
                            bundledPassengers.push({
                                type: key,
                                quantity: value
                            });
                        }
                    };
 
                    passengerInfo.forEach((p) => {
                        const hasDetails = Object.values(p.info).some(
                            (value) => value
                        );
                        const type = p.type.toUpperCase();
                        if (!hasDetails) {
                            if (!typeCount[type]) {
                                typeCount[type] = 0;
                            }
                            typeCount[type] += 1;
                        } else {
                            // If a passenger with details is found,
                            // add any accumulated counts to the bundledPassengers
                            // and then clear typeCount
                            addTypeQuantity();
                            typeCount = {};
 
                            const passengerDetails = {
                                code: p.type.toUpperCase(),
                                namePrefix: p.info.title,
                                name: p.info.firstName,
                                surname: p.info.lastName,
                                birthDate: p.info.dob,
                                passportNumber: p.info.passport,
                                ...(p.info.membershipNumber && {
                                    msNo: `TK${p.info.membershipNumber}`
                                })
                            };
 
                            Object.keys(passengerDetails).forEach((key) => {
                                if (!passengerDetails[key]) {
                                    delete passengerDetails[key];
                                }
                            });
 
                            bundledPassengers.push(passengerDetails);
                        }
                    });
 
                    // Add any remaining counts to bundledPassengers
                    // after processing all passengers
                    addTypeQuantity();
 
                    return bundledPassengers;
                })(),
                ...(serviceRequests.length > 0 && { serviceRequests }),
                contact: {
                    ...(contact.passengerIndex !== undefined
                        ? { passengerIndex: contact.passengerIndex }
                        : { fullName: contact.fullName }),
                    email: contact.email,
                    mobilePhoneNumber: {
                        countryCode: contact.mobilePhoneNumber.countryCode,
                        areaCode: parseInt(contact.mobilePhoneNumber.areaCode),
                        phoneNumber: parseInt(
                            contact.mobilePhoneNumber.phoneNumber
                        )
                    }
                },
                payment: {
                    type: selectedPaymentMethod?.id,
                    ...(selectedPaymentMethod?.id === 'CREDIT_CARD' && {
                        card: {
                            number: selectedCard.number.replaceAll(' ', ''),
                            expiration: {
                                month: parseInt(selectedCard.expMonth),
                                year: parseInt(selectedCard.expYear)
                            },
                            cvv: selectedCard.cvv
                        }
                    })
                },
                channel: channel || defaultChannel,
                ...(isAwardTicket && {
                    firstPassengerMsNo: `TK${passengerInfo[0].info.membershipNumber}`
                })
            };
 
            transaction.request = request;
 
            const response = await createPNR(request);
 
            onPNRCreated(response);
 
            transaction.response = response;
        } catch (error) {
            console.error('Error creating PNR:', error);
            openLogBottomSheet(
                error.response.data.reason,
                error.response.data.logs
            );
            transaction.response = error.response.data;
        } finally {
            setIsCreatingPNR(false);
            try {
                await createNewTransaction(transaction);
            } catch (transactionError) {
                // Silent fail for transaction saving - shouldn't affect main search flow
                console.error('Error saving transaction:', transactionError);
            }
        }
    };
 
    const validateFlightDetails = () => {
        const validateSingleFlight = (flight) =>
            flight.from &&
            flight.to &&
            ((flight.useFlightHour && flight.flightHour) ||
                (!flight.useFlightHour && flight.date));
 
        switch (tripType) {
            case 'round':
                return (
                    validateSingleFlight(outboundFlight) &&
                    validateSingleFlight(returnFlight)
                );
            case 'multi':
                return multiCityFlights.every(validateSingleFlight);
            default:
                return validateSingleFlight(outboundFlight);
        }
    };
 
    const getBookingType = () => {
        switch (pnrType) {
            case 'CASH_TICKET':
                return 'CASH_TICKET';
            case 'AWARD_TICKET_MILES_TAX':
                return 'AWARD_TICKET_MILES_TAX';
            case 'AWARD_TICKET_CASH_TAX':
                return 'AWARD_TICKET_CASH_TAX';
            case 'FREE_RESERVATION':
                return 'FREE_RESERVATION';
            case 'PRICED_RESERVATION':
                return 'PRICED_RESERVATION';
            default:
                return '';
        }
    };
 
    const handleAdditionalServicesContinue = (requestData) => {
        setServiceRequests(requestData.serviceRequests);
        setStep(4);
    };
 
    const tripTypeOnChange = (e) => {
        setTripType(e);
        setReturnFlight({
            ...defaultFlight,
            from: outboundFlight.to,
            to: outboundFlight.from,
            date: addDays(outboundFlight.date)
        });
    };
 
    const updateOutboundFlight = (field, value) => {
        setOutboundFlight((prev) => ({
            ...prev,
            [field]: value
        }));
        if (field === 'from' || field === 'to') {
            setReturnFlight((prev) => ({
                ...prev,
                from: field === 'to' ? value : prev.from,
                to: field === 'from' ? value : prev.to
            }));
        } else if (field === 'date') {
            setReturnFlight((prev) => {
                if (dayDifference(value, prev.date) < 0) {
                    return {
                        ...prev,
                        date: addDays(value)
                    };
                }
                return prev;
            });
        }
    };
 
    return (
        <div className="space-y-6">
            {step === 1 && (
                <>
                    <div className="space-y-6">
                        <ChannelSelect
                            channel={channel}
                            onChannelSelect={setChannel}
                        />
                        <BookingTypeCard
                            selectedType={pnrType}
                            onTypeSelect={setPnrType}
                        />
                    </div>
 
                    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
                        <CardHeader>
                            <CardTitle>Flight Details</CardTitle>
                            <CardDescription>
                                Enter your flight preferences
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <Tabs
                                defaultValue={tripType}
                                className="w-full"
                                value={tripType}
                                onValueChange={tripTypeOnChange}
                            >
                                <TabsList className="grid w-full grid-cols-3 mb-8">
                                    <TabsTrigger value="oneway">
                                        One Way
                                    </TabsTrigger>
                                    <TabsTrigger value="round">
                                        Round Trip
                                    </TabsTrigger>
                                    <TabsTrigger value="multi">
                                        Multi-City
                                    </TabsTrigger>
                                </TabsList>
 
                                <TabsContent
                                    value="oneway"
                                    className="space-y-6"
                                >
                                    <FlightSegment
                                        flight={outboundFlight}
                                        className="border-blue-200 dark:border-blue-800"
                                        onUpdate={(field, value) =>
                                            setOutboundFlight((prev) => ({
                                                ...prev,
                                                [field]: value
                                            }))
                                        }
                                        fareTypeOptions={fareTypeOptions}
                                    />
                                </TabsContent>
 
                                <TabsContent
                                    value="round"
                                    className="space-y-6"
                                >
                                    <div className="relative space-y-4">
                                        <FlightSegment
                                            flight={outboundFlight}
                                            onUpdate={updateOutboundFlight}
                                            className="border-blue-200 dark:border-blue-800"
                                            fareTypeOptions={fareTypeOptions}
                                        />
                                        <div className="flex items-center justify-center -my-3 relative z-10">
                                            <button
                                                onClick={swapFlights}
                                                className="bg-gradient-to-r from-blue-500 to-purple-500 text-white p-2 rounded-full hover:from-blue-600 hover:to-purple-600 transition-colors cursor-pointer"
                                                aria-label="Swap flights"
                                            >
                                                <ArrowRightLeft className="h-5 w-5" />
                                            </button>
                                        </div>
                                        <FlightSegment
                                            flight={returnFlight}
                                            onUpdate={(field, value) => {
                                                setReturnFlight((prev) => ({
                                                    ...prev,
                                                    [field]: value
                                                }));
                                                if (
                                                    field === 'from' ||
                                                    field === 'to'
                                                ) {
                                                    setOutboundFlight(
                                                        (prev) => ({
                                                            ...prev,
                                                            from:
                                                                field === 'to'
                                                                    ? value
                                                                    : prev.from,
                                                            to:
                                                                field === 'from'
                                                                    ? value
                                                                    : prev.to
                                                        })
                                                    );
                                                }
                                            }}
                                            calendarDisabledBefore={
                                                outboundFlight.date
                                            }
                                            className="border-purple-200 dark:border-purple-800"
                                            fareTypeOptions={fareTypeOptions}
                                        />
                                    </div>
                                </TabsContent>
 
                                <TabsContent
                                    value="multi"
                                    className="space-y-6"
                                >
                                    {multiCityFlights.map((flight, index) => (
                                        <FlightSegment
                                            key={keyGenerator()}
                                            flight={flight}
                                            onUpdate={(field, value) =>
                                                updateMultiCityFlight(
                                                    index,
                                                    field,
                                                    value
                                                )
                                            }
                                            onRemove={() => removeFlight(index)}
                                            showRemove={
                                                multiCityFlights.length > 2
                                            }
                                            className={cn(
                                                'transition-all duration-200',
                                                index !==
                                                    multiCityFlights.length -
                                                        1 &&
                                                    'border-blue-200 dark:border-blue-800',
                                                index ===
                                                    multiCityFlights.length -
                                                        1 &&
                                                    'border-purple-200 dark:border-purple-800'
                                            )}
                                            fareTypeOptions={fareTypeOptions}
                                            calendarDisabledBefore={
                                                index === 0
                                                    ? new Date()
                                                    : multiCityFlights[
                                                          index - 1
                                                      ].date
                                            }
                                        />
                                    ))}
                                    {multiCityFlights.length < 5 && (
                                        <Button
                                            variant="outline"
                                            onClick={addFlight}
                                            className="w-full"
                                        >
                                            Add Another Flight
                                        </Button>
                                    )}
                                </TabsContent>
                            </Tabs>
 
                            <PassengerSelect
                                value={passengers}
                                onChange={setPassengers}
                            />
 
                            <Button
                                className="w-full"
                                onClick={() => {
                                    // Initialize passenger info for all passenger types
                                    const newPassengerInfo = Object.entries(
                                        passengers
                                    ).flatMap(([type, count]) =>
                                        Array(count)
                                            .fill()
                                            .map(() => ({
                                                key: keyGenerator(),
                                                type,
                                                info: {
                                                    title: '',
                                                    firstName: '',
                                                    lastName: '',
                                                    dob: '',
                                                    passport: ''
                                                }
                                            }))
                                    );
                                    setPassengerInfo(newPassengerInfo);
                                    setStep(2);
                                }}
                                disabled={!validateFlightDetails() || !pnrType}
                            >
                                Continue to Passenger Information
                            </Button>
                        </CardContent>
                    </Card>
                </>
            )}
 
            {step === 2 && (
                <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
                    <CardHeader>
                        <CardTitle>Passenger Information</CardTitle>
                        <CardDescription>
                            Enter details for all passengers
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {pnrType.startsWith('AWARD_TICKET_') && (
                            <Alert
                                variant="warning"
                                className="flex items-start space-x-3"
                            >
                                <i className="icon-tk-milesandsmiles custom-icon-small text-white" />
                                <div>
                                    <AlertTitle>
                                        Miles&Smiles Membership Required
                                    </AlertTitle>
                                    <AlertDescription>
                                        For award ticket bookings, the first
                                        passenger must have a valid Miles&Smiles
                                        membership number. Please enter the
                                        membership number before proceeding to
                                        payment.
                                    </AlertDescription>
                                </div>
                            </Alert>
                        )}
 
                        {!validateContactDetails() && (
                            <Alert
 
 
                                variant="destructive"
                                className="flex items-start space-x-3"
                            >
                                <Phone className="h-4 w-4 mt-1" />
                                <div>
                                    <AlertTitle>
                                        Contact Information Required
                                    </AlertTitle>
                                    <AlertDescription>
                                        Contact information is mandatory for PNR
                                        creation. Please provide complete
                                        contact details including name, email,
                                        and mobile phone number.
                                    </AlertDescription>
                                </div>
                            </Alert>
                        )}
 
                        {passengerInfo.some((passenger) =>
                            isMsReqPassengerType(passenger)
                        ) && (
                            <Alert
                                variant="info"
                                className="flex items-start space-x-3"
                            >
                                <i className="icon-tk-milesandsmiles custom-icon-small text-white" />
                                <div>
                                    <AlertTitle>
                                        Miles&Smiles Membership Number Required
                                        for Discount Eligibility
                                    </AlertTitle>
                                    <AlertDescription>
                                        To apply discounts for{' '}
                                        {[
                                            ...new Set(
                                                passengerInfo
                                                    .filter((pas) =>
                                                        isMsReqPassengerType(
                                                            pas
                                                        )
                                                    )
                                                    .map((pas) => pas.type)
                                            )
                                        ].join(', ')}{' '}
                                        passenger types, please provide an
                                        Miles&Smiles Membership number.
                                    </AlertDescription>
                                </div>
                            </Alert>
                        )}
 
                        <PassengerDetails
                            passengers={passengerInfo}
                            onPassengersChange={setPassengerInfo}
                            totalPassengers={passengers}
                            showDetails={showPassengerDetails}
                            onToggleDetails={() =>
                                setShowPassengerDetails(!showPassengerDetails)
                            }
                            pnrType={pnrType}
                        />
 
                        <ContactDetails
                            passengers={passengerInfo}
                            contact={contact}
                            onContactChange={setContact}
                            showDetails={showContactDetails}
                            onToggleDetails={() =>
                                setShowContactDetails(!showContactDetails)
                            }
                        />
 
                        <div className="flex gap-3">
                            <Button
                                variant="outline"
                                onClick={() => setStep(1)}
                                className="w-24"
                            >
                                Back
                            </Button>
                            <Button
                                className="flex-1"
                                onClick={() => setStep(3)}
                                disabled={
                                    (pnrType.startsWith('AWARD_TICKET_') &&
                                        !validateMilesBookingPassengers(
                                            passengerInfo,
                                            pnrType
                                        ).isValid) ||
                                    !validateContactDetails()
                                }
                            >
                                {(pnrType.startsWith('AWARD_TICKET_') &&
                                    !validateMilesBookingPassengers(
                                        passengerInfo,
                                        pnrType
                                    ).isValid) ||
                                !validateContactDetails() ? (
                                    <AlertCircle className="mr-2 h-4 w-4" />
                                ) : null}
                                <>
                                    <PlusIcon className="mr-2 h-5 w-5" />
                                    Add Additional Services
                                </>
                            </Button>
                            <Button
                                className="flex-1"
                                onClick={() => setStep(4)}
                                disabled={
                                    (pnrType.startsWith('AWARD_TICKET_') &&
                                        !validateMilesBookingPassengers(
                                            passengerInfo,
                                            pnrType
                                        ).isValid) ||
                                    !validateContactDetails()
                                }
                            >
                                {(pnrType.startsWith('AWARD_TICKET_') &&
                                    !validateMilesBookingPassengers(
                                        passengerInfo,
                                        pnrType
                                    ).isValid) ||
                                !validateContactDetails() ? (
                                    <AlertCircle className="mr-2 h-4 w-4" />
                                ) : null}
                                <>
                                    <CreditCard className="mr-2 h-5 w-5" />
                                    Jump To Payment
                                </>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            )}
 
            {step === 3 && (
                <AdditionalServicesCard
                    passengers={passengers}
                    flights={getFlights()}
                    searchButtonActive={false}
                    onClickBack={() => setStep(2)}
                    onClickContinue={handleAdditionalServicesContinue}
                />
            )}
 
            {step === 4 && (
                <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
                    <CardHeader>
                        <CardTitle>Payment</CardTitle>
                        <CardDescription>
                            {pnrType.startsWith('AWARD_TICKET_')
                                ? 'Miles&Smiles membership required for miles bookings'
                                : 'Select your preferred payment method'}
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <PaymentMethodSelect
                            selectedMethod={selectedPaymentMethod?.id}
                            onMethodSelect={(method) =>
                                setSelectedPaymentMethod(method)
                            }
                            bookingType={getBookingType()}
                        />
                        {selectedPaymentMethod?.id === 'CREDIT_CARD' &&
                            pnrType !== 'AWARD_TICKET_MILES_TAX' && (
                                <PaymentCardInformation
                                    savedCards={savedCards}
                                    setSavedCards={setSavedCards}
                                    selectedCard={selectedCard}
                                    setSelectedCard={setSelectedCard}
                                    newCardView={newCardView}
                                    setNewCardView={setNewCardView}
                                    saveForFuture={saveForFuture}
                                    setSaveForFuture={setSaveForFuture}
                                />
                            )}
 
                        {/* Add SaveTransactionToggle with name support */}
                        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                            <SaveTransactionToggle
                                value={saveTransaction}
                                onChange={setSaveTransaction}
                                label="Save this booking to favorites"
                                className="mt-2"
                                defaultName={transactionName}
                                onNameChange={setTransactionName}
                            />
                        </div>
 
                        <div className="flex gap-3">
                            <Button
                                variant="outline"
                                onClick={() => setStep(3)}
                            >
                                Back
                            </Button>
                            <Button
                                className="flex-1 bg-gradient-to-r from-rose-600 to-purple-600 hover:from-rose-700 hover:to-purple-700"
                                onClick={handleCreatePNR}
                                disabled={
                                    isCreatingPNR ||
                                    !selectedPaymentMethod ||
                                    !channel ||
                                    (selectedPaymentMethod?.id ===
                                        'CREDIT_CARD' &&
                                        !validateCreditCard(selectedCard))
                                }
                            >
                                {isCreatingPNR ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Creating PNR...
                                    </>
                                ) : (
                                    'Create PNR'
                                )}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
