'use client';
 
import React, { useEffect, useState } from 'react';
import { PNRCreation } from '@/components/pnr/PNRCreation';
import { PNRDetails } from '@/components/pnr/PNRDetails';
import { useSearchParams } from 'next/navigation';
import PortListProvider from '@/context/portList/PortListProvider';
import { useMatomo } from '@jonkoops/matomo-tracker-react';
 
function PNRPage() {
    const [pnrDetails, setPNRDetails] = useState(null);
    const searchParams = useSearchParams();
 
    const initialFormData = {
        from: searchParams.get('from') || 'IST',
        to: searchParams.get('to'),
        passengers: parseInt(searchParams.get('passengers')) || 1,
        tripType: searchParams.get('type') || 'oneway'
    };
 
    const { trackPageView } = useMatomo();
 
    useEffect(() => {
        trackPageView();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Run only once on mount to prevent infinite loop
 
    return (
        <PortListProvider>
            <div className="max-w-4xl mx-auto space-y-8">
                <div className="text-center">
                    <h1 className="text-4xl font-bold text-[#E81932] dark:text-white mb-4">
                        PNR Creation
                    </h1>
                    <p className="text-gray-600 dark:text-gray-300">
                        Create and manage passenger reservations
                    </p>
                </div>
 
                {!pnrDetails ? (
                    <PNRCreation
                        onPNRCreated={setPNRDetails}
                        initialData={initialFormData}
                    />
                ) : (
                    <PNRDetails
                        pnr={pnrDetails}
                        onNewPNR={() => setPNRDetails(null)}
                    />
                )}
            </div>
        </PortListProvider>
    );
}
 
export default PNRPage;