import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '@/components/ui/tooltip';
import {
    DEFAULT_COUNTRY_CODE,
    getCountryByCode,
    getCountryOptions
} from '@/lib/client/constants/countries';
import { PASSENGER_TYPES } from '@/lib/client/constants/passengers';
import { cn } from '@/lib/client/utils';
import {
    canEditPassengerDetails,
    isMsReqPassengerType,
    sanitizePassengerData
} from '@/lib/client/validations/passenger';
import {
    PASSENGER_TITLES,
    validateEmail,
    validatePhoneNumber,
    validateAreaCode,
    hasCompleteContactInfo
} from '@/lib/client/validations/passenger';
import {
    Accessibility,
    Baby,
    BadgeInfo,
    ChevronDown,
    ChevronUp,
    Mail,
    Phone,
    Pickaxe,
    User,
    Users,
    Check,
    AlertCircle
} from 'lucide-react';
 
export function PassengerDetails({
    passengers,
    onPassengersChange,
    totalPassengers,
    showDetails,
    onToggleDetails,
    pnrType
}) {
    const [expandedPassenger, setExpandedPassenger] = useState(-1);
 
    const getTotalPassengerCount = () => {
        if (!totalPassengers) {
            return 0;
        }
        return Object.values(PASSENGER_TYPES).reduce((total, type) => {
            return total + (totalPassengers[type.code] || 0);
        }, 0);
    };
 
    const getPassengerIcon = (type) => {
        switch (type) {
            case 'DISABLED':
                return Accessibility;
            case 'LABOR':
                return Pickaxe;
            case 'INFANT':
            case 'ETHNIC_INFANT':
            case 'LABOR_INFANT':
                return Baby;
            case 'CHILD':
            case 'ETHNIC_CHILD':
            case 'LABOR_CHILD':
                return Users;
            default:
                return User;
        }
    };
 
    const updatePassenger = (index, field, value) => {
        const updatedPassengers = passengers.map((passenger, i) => {
            if (i === index) {
                let updatedPassenger = {
                    ...passenger,
                    info: { ...passenger.info, [field]: value }
                };
 
                // Sanitize data if passenger is infant
                updatedPassenger = sanitizePassengerData(updatedPassenger);
 
                return updatedPassenger;
            }
            return passenger;
        });
        onPassengersChange(updatedPassengers);
    };
 
    const isValidEmail = (email) => {
        return validateEmail(email);
    };
 
    const isValidPhoneNumber = (phoneNumber) => {
        return validatePhoneNumber(phoneNumber);
    };
 
    const isValidAreaCode = (areaCode) => {
        return validateAreaCode(areaCode);
    };
 
    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-primary" />
                    <span className="font-medium">
                        Passenger Details (Optional)
                    </span>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <div>
                                    <BadgeInfo className="h-4 w-4 text-muted-foreground" />
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>All passenger details are optional.</p>
                                <p>Fill in only what you need.</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                    <Badge variant="outline" className="ml-2">
                        {passengers?.length || 0}/{getTotalPassengerCount()}
                    </Badge>
                </div>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                        if (!showDetails) {
                            setExpandedPassenger(0);
                        }
                        onToggleDetails();
                    }}
                    className="gap-2"
                >
                    {showDetails ? (
                        <>
                            Details <ChevronUp className="h-4 w-4" />
                        </>
                    ) : (
                        <>
                            Details <ChevronDown className="h-4 w-4" />
                        </>
                    )}
                </Button>
            </div>
 
            {showDetails && (
                <div className="space-y-3">
                    {passengers.map((passenger, index) => (
                        <Card
                            key={passenger.key}
                            className={cn(
                                'transition-all duration-200',
                                expandedPassenger === index
                                    ? 'ring-2 ring-primary'
                                    : ''
                            )}
                        >
                            <CardContent className="p-4">
                                <div
                                    className="flex items-center justify-between cursor-pointer"
                                    onClick={() =>
                                        setExpandedPassenger(
                                            expandedPassenger === index
                                                ? -1
                                                : index
                                        )
                                    }
                                >
                                    <div className="flex items-center gap-3">
                                        {React.createElement(
                                            getPassengerIcon(passenger.type),
                                            {
                                                className:
                                                    'h-5 w-5 text-muted-foreground'
                                            }
                                        )}
                                        <span className="font-medium">
                                            {passenger.info.firstName ||
                                            passenger.info.lastName ? (
                                                `${passenger.info.firstName} ${passenger.info.lastName}`.trim()
                                            ) : (
                                                <>
                                                    {passenger.type
                                                        .charAt(0)
                                                        .toUpperCase() +
                                                        passenger.type.slice(
                                                            1
                                                        )}{' '}
                                                    {index + 1}
                                                    <Badge
                                                        variant="secondary"
                                                        className="ml-2 text-xs"
                                                    >
                                                        Optional Details
                                                    </Badge>
                                                </>
                                            )}
                                        </span>
                                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                            {passenger.info.passport && (
                                                <span>
                                                    {passenger.info.passport}
                                                </span>
                                            )}
                                            {passenger.info.email && (
                                                <Badge
                                                    variant={
                                                        isValidEmail(
                                                            passenger.info.email
                                                        )
                                                            ? 'outline'
                                                            : 'destructive'
                                                    }
                                                    className="text-xs"
                                                >
                                                    <Mail className="h-3 w-3 mr-1" />
                                                    {passenger.info.email}
                                                    {!isValidEmail(
                                                        passenger.info.email
                                                    ) && (
                                                        <AlertCircle className="h-3 w-3 ml-1" />
                                                    )}
                                                </Badge>
                                            )}
                                            {passenger.info.countryCode &&
                                                passenger.info.areaCode &&
                                                passenger.info.phoneNumber && (
                                                    <Badge
                                                        variant={
                                                            isValidAreaCode(
                                                                passenger.info
                                                                    .areaCode
                                                            ) &&
                                                            isValidPhoneNumber(
                                                                passenger.info
                                                                    .phoneNumber
                                                            )
                                                                ? 'outline'
                                                                : 'destructive'
                                                        }
                                                        className="text-xs"
                                                    >
                                                        <Phone className="h-3 w-3 mr-1" />
                                                        +
                                                        {
                                                            getCountryByCode(
                                                                passenger.info
                                                                    .countryCode
                                                            ).accessCode
                                                        }{' '}
                                                        {
                                                            passenger.info
                                                                .areaCode
                                                        }{' '}
                                                        {
                                                            passenger.info
                                                                .phoneNumber
                                                        }
                                                        {(!isValidAreaCode(
                                                            passenger.info
                                                                .areaCode
                                                        ) ||
                                                            !isValidPhoneNumber(
                                                                passenger.info
                                                                    .phoneNumber
                                                            )) && (
                                                            <AlertCircle className="h-3 w-3 ml-1" />
                                                        )}
                                                    </Badge>
                                                )}
                                            {hasCompleteContactInfo(
                                                passenger
                                            ) &&
                                                passenger.type !== 'infant' && (
                                                    <Badge
                                                        variant="default"
                                                        className="text-xs bg-green-500 hover:bg-green-600"
                                                    >
                                                        <Check className="h-3 w-3 mr-1" />
                                                        Contact Ready
                                                    </Badge>
                                                )}
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {expandedPassenger === index ? (
                                            <ChevronUp className="h-4 w-4" />
                                        ) : (
                                            <ChevronDown className="h-4 w-4" />
                                        )}
                                    </div>
                                </div>
 
                                {expandedPassenger === index && (
                                    <div className="mt-4 space-y-4">
                                        {passenger.type !== 'infant' && (
                                            <div className="space-y-1">
                                                <Label>Membership Number</Label>
                                                <Input
                                                    value={
                                                        passenger.info
                                                            .membershipNumber
                                                    }
                                                    onChange={(e) =>
                                                        updatePassenger(
                                                            index,
                                                            'membershipNumber',
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Enter Miles&Smiles number"
                                                    maxLength={9}
                                                    pattern="[0-9]*"
                                                />
 
                                                {index === 0 &&
                                                    pnrType.startsWith(
                                                        'AWARD_TICKET'
                                                    ) && (
                                                        <div className="flex p-2 space-y-1">
                                                            <div className="flex-initial">
                                                                <p className="text-xs text-muted-foreground text-red-300">
                                                                    Membership
                                                                    number is
                                                                    required
                                                                </p>
 
                                                            </div>
                                                        </div>
                                                    )}
                                                {!isMsReqPassengerType(
                                                    passenger
                                                ) &&
                                                    index !== 0 &&
                                                    pnrType.startsWith(
                                                        'AWARD_TICKET'
                                                    ) && (
                                                        <div className="flex p-2 space-y-1">
                                                            <div className="flex-initial">
                                                                <p className="text-xs text-muted-foreground">
                                                                    Optional -
                                                                    Enter
                                                                    9-digit
                                                                    Miles&Smiles
                                                                    number
                                                                </p>
                                                            </div>
                                                        </div>
                                                    )}
 
                                                {isMsReqPassengerType(
                                                    passenger
                                                ) && (
                                                    <div className="flex p-2 space-y-1">
                                                        <div className="flex-initial">
                                                            <p className="text-xs text-muted-foreground">
                                                                Optional - Enter
                                                                9-digit
                                                                Miles&Smiles
                                                                number for
                                                                Discount
                                                                Eligibility
                                                            </p>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        )}
 
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-1">
                                                <Label>Title</Label>
                                                <Select
                                                    value={passenger.info.title}
                                                    onValueChange={(value) =>
                                                        updatePassenger(
                                                            index,
                                                            'title',
                                                            value
                                                        )
                                                    }
                                                    disabled={
                                                        !canEditPassengerDetails(
                                                            passenger
                                                        )
                                                    }
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select title" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {PASSENGER_TITLES.map(
                                                            (title) => (
                                                                <SelectItem
                                                                    key={
                                                                        title.value
                                                                    }
                                                                    value={
                                                                        title.value
                                                                    }
                                                                >
                                                                    {
                                                                        title.label
                                                                    }
                                                                </SelectItem>
                                                            )
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                            </div>
 
                                            <div className="space-y-1">
                                                <Label>First Name</Label>
                                                <Input
                                                    value={
                                                        passenger.info.firstName
                                                    }
                                                    onChange={(e) =>
                                                        updatePassenger(
                                                            index,
                                                            'firstName',
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Enter first name"
                                                    disabled={
                                                        !canEditPassengerDetails(
                                                            passenger
                                                        )
                                                    }
                                                />
                                            </div>
                                        </div>
 
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-1">
                                                <Label>Last Name</Label>
                                                <Input
                                                    value={
                                                        passenger.info.lastName
                                                    }
                                                    onChange={(e) =>
                                                        updatePassenger(
                                                            index,
                                                            'lastName',
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Enter last name"
                                                    disabled={
                                                        !canEditPassengerDetails(
                                                            passenger
                                                        )
                                                    }
                                                />
                                            </div>
 
                                            <div className="space-y-1">
                                                <Label>Date of Birth</Label>
                                                <Input
                                                    type="date"
                                                    value={passenger.info.dob}
                                                    onChange={(e) =>
                                                        updatePassenger(
                                                            index,
                                                            'dob',
                                                            e.target.value
                                                        )
                                                    }
                                                    disabled={
                                                        !canEditPassengerDetails(
                                                            passenger
                                                        )
                                                    }
                                                />
                                            </div>
                                        </div>
 
                                        <div className="space-y-1">
                                            <Label>Passport Number</Label>
                                            <Input
                                                value={passenger.info.passport}
                                                onChange={(e) =>
                                                    updatePassenger(
                                                        index,
                                                        'passport',
                                                        e.target.value
                                                    )
                                                }
                                                placeholder="Enter passport number"
                                                disabled={
                                                    !canEditPassengerDetails(
                                                        passenger
                                                    )
                                                }
                                            />
                                        </div>
 
                                        {passenger.type !== 'infant' && (
                                            <>
                                                <div className="space-y-1">
                                                    <Label>
                                                        Email Address (Optional)
                                                    </Label>
                                                    <Input
                                                        type="email"
                                                        value={
                                                            passenger.info
                                                                .email || ''
                                                        }
                                                        onChange={(e) =>
                                                            updatePassenger(
                                                                index,
                                                                'email',
                                                                e.target.value
                                                            )
                                                        }
                                                        placeholder="Enter email address"
                                                        className={cn(
                                                            passenger.info
                                                                .email &&
                                                                !isValidEmail(
                                                                    passenger
                                                                        .info
                                                                        .email
                                                                )
                                                                ? 'border-red-300 focus:border-red-500'
                                                                : passenger.info
                                                                        .email &&
                                                                    isValidEmail(
                                                                        passenger
                                                                            .info
                                                                            .email
                                                                    )
                                                                  ? 'border-green-300 focus:border-green-500'
                                                                  : ''
                                                        )}
                                                        disabled={
                                                            !canEditPassengerDetails(
                                                                passenger
                                                            )
                                                        }
                                                    />
                                                    {passenger.info.email &&
                                                        !isValidEmail(
                                                            passenger.info.email
                                                        ) && (
                                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                                <AlertCircle className="h-3 w-3" />
                                                                Please enter a
                                                                valid email
                                                                address
                                                            </p>
                                                        )}
                                                    {passenger.info.email &&
                                                        isValidEmail(
                                                            passenger.info.email
                                                        ) && (
                                                            <p className="text-sm text-green-600 flex items-center gap-1">
                                                                <Check className="h-3 w-3" />
                                                                Valid email
                                                                address
                                                            </p>
                                                        )}
                                                </div>
 
                                                <div className="space-y-2">
                                                    <Label>
                                                        Mobile Phone (Optional)
                                                    </Label>
                                                    <div className="grid grid-cols-3 gap-2">
                                                        <div className="space-y-1">
                                                            <Label className="text-xs text-muted-foreground">
                                                                Country
                                                            </Label>
                                                            <Select
                                                                value={
                                                                    passenger
                                                                        .info
                                                                        .countryCode ||
                                                                    DEFAULT_COUNTRY_CODE
                                                                }
                                                                onValueChange={(
                                                                    value
                                                                ) =>
                                                                    updatePassenger(
                                                                        index,
                                                                        'countryCode',
                                                                        value
                                                                    )
                                                                }
                                                                disabled={
                                                                    !canEditPassengerDetails(
                                                                        passenger
                                                                    )
                                                                }
                                                            >
                                                                <SelectTrigger className="h-9">
                                                                    <SelectValue>
                                                                        <div className="flex items-center gap-1">
                                                                            <span>
                                                                                {
                                                                                    getCountryByCode(
                                                                                        passenger
                                                                                            .info
                                                                                            .countryCode ||
                                                                                            DEFAULT_COUNTRY_CODE
                                                                                    )
                                                                                        .flag
                                                                                }
                                                                            </span>
                                                                            <span className="text-xs">
                                                                                +
                                                                                {
                                                                                    getCountryByCode(
                                                                                        passenger
                                                                                            .info
                                                                                            .countryCode ||
                                                                                            DEFAULT_COUNTRY_CODE
                                                                                    )
                                                                                        .accessCode
                                                                                }
                                                                            </span>
                                                                        </div>
                                                                    </SelectValue>
                                                                </SelectTrigger>
                                                                <SelectContent>
                                                                    {getCountryOptions()
                                                                        .slice(
                                                                            0,
                                                                            20
                                                                         )
                                                                        .map(
                                                                            (
                                                                                country
                                                                            ) => (
                                                                                <SelectItem
                                                                                    key={
                                                                                        country.value
                                                                                    }
                                                                                    value={
                                                                                        country.value
                                                                                    }
                                                                                >
                                                                                    <div className="flex items-center gap-2">
                                                                                        <span>
                                                                                            {
                                                                                                country.flag
                                                                                            }
                                                                                        </span>
                                                                                        <span className="text-xs">
                                                                                            +
                                                                                            {
                                                                                                country.accessCode
                                                                                            }
                                                                                        </span>
                                                                                        <span className="text-xs truncate">
                                                                                            {
                                                                                                country.name
                                                                                            }
                                                                                        </span>
                                                                                    </div>
                                                                                </SelectItem>
                                                                            )
                                                                        )}
                                                                </SelectContent>
                                                            </Select>
                                                        </div>
                                                        <div className="space-y-1">
                                                            <Label className="text-xs text-muted-foreground">
                                                                Area Code
                                                            </Label>
                                                            <Input
                                                                value={
                                                                    passenger
                                                                        .info
                                                                        .areaCode ||
                                                                    ''
                                                                }
                                                                onChange={(
                                                                    e
                                                                ) => {
                                                                    const value =
                                                                        e.target.value
                                                                            .replace(
                                                                                /\D/g,
                                                                                ''
                                                                            )
                                                                            .slice(
                                                                                0,
                                                                                3
                                                                            );
                                                                    updatePassenger(
                                                                        index,
                                                                        'areaCode',
                                                                        value
                                                                    );
                                                                }}
                                                                placeholder="541"
                                                                maxLength={3}
                                                                className={cn(
                                                                    'h-9',
                                                                    passenger
                                                                        .info
                                                                        .areaCode &&
                                                                        !isValidAreaCode(
                                                                            passenger
                                                                                .info
                                                                                .areaCode
                                                                        )
                                                                        ? 'border-red-300 focus:border-red-500'
                                                                        : passenger
                                                                                .info
                                                                                .areaCode &&
                                                                            isValidAreaCode(
                                                                                passenger
                                                                                    .info
                                                                                    .areaCode
                                                                            )
                                                                          ? 'border-green-300 focus:border-green-500'
                                                                          : 'h-9'
                                                                )}
                                                                disabled={
                                                                    !canEditPassengerDetails(
                                                                        passenger
                                                                    )
                                                                }
                                                            />
                                                            {passenger.info
                                                                .areaCode &&
                                                                !isValidAreaCode(
                                                                    passenger
                                                                        .info
                                                                        .areaCode
                                                                ) && (
                                                                    <p className="text-xs text-red-500 flex items-center gap-1">
                                                                        <AlertCircle className="h-2 w-2" />
                                                                        3 digits
                                                                    </p>
                                                                )}
                                                        </div>
                                                        <div className="space-y-1">
                                                            <Label className="text-xs text-muted-foreground">
                                                                Phone
                                                            </Label>
                                                            <Input
                                                                value={
                                                                    passenger
                                                                        .info
                                                                        .phoneNumber ||
                                                                    ''
                                                                }
                                                                onChange={(
                                                                    e
                                                                ) => {
                                                                    const value =
                                                                        e.target.value
                                                                            .replace(
                                                                                /\D/g,
                                                                                ''
                                                                            )
                                                                            .slice(
                                                                                0,
                                                                                10
                                                                            );
                                                                    updatePassenger(
                                                                        index,
                                                                        'phoneNumber',
                                                                        value
                                                                    );
                                                                }}
                                                                placeholder="1234567"
                                                                maxLength={10}
                                                                className={cn(
                                                                    'h-9',
                                                                    passenger
                                                                        .info
                                                                        .phoneNumber &&
                                                                        !isValidPhoneNumber(
                                                                            passenger
                                                                                .info
                                                                                .phoneNumber
                                                                        )
                                                                        ? 'border-red-300 focus:border-red-500'
                                                                        : passenger
                                                                                .info
                                                                                .phoneNumber &&
                                                                            isValidPhoneNumber(
                                                                                passenger
                                                                                    .info
                                                                                    .phoneNumber
                                                                            )
                                                                          ? 'border-green-300 focus:border-green-500'
                                                                          : 'h-9'
                                                                )}
                                                                disabled={
                                                                    !canEditPassengerDetails(
                                                                        passenger
                                                                    )
                                                                }
                                                            />
                                                            {passenger.info
                                                                .phoneNumber &&
                                                                !isValidPhoneNumber(
                                                                    passenger
                                                                        .info
                                                                        .phoneNumber
                                                                ) && (
                                                                    <p className="text-xs text-red-500 flex items-center gap-1">
                                                                        <AlertCircle className="h-2 w-2" />
                                                                        6-10
                                                                        digits
                                                                    </p>
                                                                )}
                                                        </div>
                                                    </div>
 
                                                    {/* Phone validation summary */}
                                                    <div className="mt-2">
                                                        {passenger.info
                                                            .areaCode &&
                                                            passenger.info
                                                                .phoneNumber &&
                                                            isValidAreaCode(
                                                                passenger.info
                                                                    .areaCode
                                                            ) &&
                                                            isValidPhoneNumber(
                                                                passenger.info
                                                                    .phoneNumber
                                                            ) && (
                                                                <p className="text-sm text-green-600 flex items-center gap-1">
                                                                    <Check className="h-4 w-4" />
                                                                    Complete: +
                                                                    {
                                                                        getCountryByCode(
                                                                            passenger
                                                                                .info
                                                                                .countryCode ||
                                                                                DEFAULT_COUNTRY_CODE
                                                                        )
                                                                            .accessCode
                                                                    }{' '}
                                                                    {
                                                                        passenger
                                                                            .info
                                                                            .areaCode
                                                                    }{' '}
                                                                    {
                                                                        passenger
                                                                            .info
                                                                            .phoneNumber
                                                                    }
                                                                </p>
                                                            )}
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
}
 
 