<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

Never ever change the ui of the refactored components unless you have a very great idea

# PNR Component Refactor Project

This is a Next.js project for Passenger Name Record (PNR) management with comprehensive testing.

## Completed Setup Tasks

- [x] Verify that the copilot-instructions.md file in the .github directory is created.
- [x] Clarify Project Requirements - Project requirements: Next.js with TypeScript, PNR components, comprehensive testing setup with Jest, Tailwind CSS, React Hook Form, Zod validation, and Radix UI components
- [x] Scaffold the Project - Next.js project structure created with TypeScript, Tailwind CSS, App Router, and src directory
- [x] Customize the Project - Created PNR component structure with types, schemas, and basic project layout
- [x] Install Required Extensions - No specific extensions required for this project type
- [x] Compile the Project - Dependencies installed successfully, TypeScript compilation passes without errors
- [x] Create and Run Task - Development server task created and running on http://localhost:3000
- [x] Launch the Project - Project is running at http://localhost:3000
- [x] Ensure Documentation is Complete - README.md created with comprehensive project information

## Project Status

✅ Project setup complete and ready for development!

- Development server running at: http://localhost:3000
- All dependencies installed
- TypeScript compilation successful
- Testing framework configured
- Ready for PNR component development
