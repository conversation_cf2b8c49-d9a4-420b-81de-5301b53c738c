import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export function ChannelSelect({ channel, onChannelSelect }) {
  const options = ['DEFAULT', 'B2B']
  return (
    <Card className="border-0 ring-1 ring-gray-200 dark:ring-gray-700">
      <CardContent className="p-4 flex gap-2">
        {options.map((opt) => (
          <Button key={opt} variant={channel === opt ? undefined : 'outline'} onClick={() => onChannelSelect(opt)}>
            {opt}
          </Button>
        ))}
      </CardContent>
    </Card>
  )
}


