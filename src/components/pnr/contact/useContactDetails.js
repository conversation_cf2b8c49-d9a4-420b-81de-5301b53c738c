import { useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import {
  getCountryOptions,
  getCountryByCode,
  DEFAULT_COUNTRY_CODE,
  DEFAULT_AREA_CODE,
} from '@/lib/client/constants/countries'
import {
  contactSchema,
  validateCompleteContact,
  formatPhoneNumber,
  canPassengerBeContact,
} from '@/lib/client/validations/contact'

export default function useContactDetails({ passengers = [], contact, onContactChange }) {
  const [expandedContact, setExpandedContact] = useState(false)
  const [contactType, setContactType] = useState('unassociated')
  const [selectedPassengerIndex, setSelectedPassengerIndex] = useState(0)
  const [countryCodeOpen, setCountryCodeOpen] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [copied, setCopied] = useState(false)

  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(contactSchema),
    mode: 'onChange',
    defaultValues: {
      fullName: contact?.fullName || '',
      email: contact?.email || '',
      mobilePhoneNumber: {
        countryCode: contact?.mobilePhoneNumber?.countryCode || DEFAULT_COUNTRY_CODE,
        areaCode: contact?.mobilePhoneNumber?.areaCode || DEFAULT_AREA_CODE,
        phoneNumber: contact?.mobilePhoneNumber?.phoneNumber || '',
      },
      passengerIndex: contact?.passengerIndex,
    },
  })

  const watchedValues = watch()
  const countryOptions = useMemo(() => getCountryOptions(), [])

  const passengersWithContact = useMemo(
    () => passengers.filter(canPassengerBeContact),
    [passengers]
  )

  const hasPassengersWithContact = passengersWithContact.length > 0

  useEffect(() => {
    onContactChange?.(watchedValues)
  }, [watchedValues, onContactChange])

  useEffect(() => {
    if (!contact) {
      const defaultContact = {
        fullName: '',
        email: '',
        mobilePhoneNumber: {
          countryCode: DEFAULT_COUNTRY_CODE,
          areaCode: DEFAULT_AREA_CODE,
          phoneNumber: '',
        },
      }

      if (hasPassengersWithContact && contactType === 'associated') {
        const passenger = passengersWithContact[0]
        defaultContact.passengerIndex = passengers.indexOf(passenger)
        if (passenger.info.email) defaultContact.email = passenger.info.email
        if (passenger.info.firstName || passenger.info.lastName) {
          defaultContact.fullName = `${passenger.info.firstName} ${passenger.info.lastName}`.trim()
        }
        if (passenger.info.countryCode && passenger.info.areaCode && passenger.info.phoneNumber) {
          defaultContact.mobilePhoneNumber = {
            countryCode: passenger.info.countryCode,
            areaCode: passenger.info.areaCode,
            phoneNumber: passenger.info.phoneNumber,
          }
        }
      }

      onContactChange?.(defaultContact)
    }
  }, [contact, onContactChange, hasPassengersWithContact, contactType, passengers, passengersWithContact])

  useEffect(() => {
    if (
      hasPassengersWithContact &&
      contactType === 'unassociated' &&
      (!contact?.fullName || !contact?.email || !contact?.mobilePhoneNumber?.phoneNumber)
    ) {
      setContactType('associated')
    }
  }, [hasPassengersWithContact, contactType, contact])

  const handleContactTypeChange = (type) => {
    setContactType(type)
    if (type === 'associated' && passengersWithContact.length > 0) {
      const passenger = passengersWithContact[selectedPassengerIndex] || passengersWithContact[0]
      const passengerIndex = passengers.indexOf(passenger)
      setValue('passengerIndex', passengerIndex)
      if (passenger.info.email) setValue('email', passenger.info.email)
      if (passenger.info.firstName || passenger.info.lastName) {
        setValue('fullName', `${passenger.info.firstName} ${passenger.info.lastName}`.trim())
      }
      if (passenger.info.countryCode && passenger.info.areaCode && passenger.info.phoneNumber) {
        setValue('mobilePhoneNumber', {
          countryCode: passenger.info.countryCode,
          areaCode: passenger.info.areaCode,
          phoneNumber: passenger.info.phoneNumber,
        })
      }
    } else {
      setValue('passengerIndex', undefined)
    }
  }

  const handlePassengerSelection = (index) => {
    setSelectedPassengerIndex(index)
    const passenger = passengersWithContact[index]
    const actualPassengerIndex = passengers.indexOf(passenger)
    setValue('passengerIndex', actualPassengerIndex)
    if (passenger.info.email) setValue('email', passenger.info.email)
    if (passenger.info.firstName || passenger.info.lastName) {
      setValue('fullName', `${passenger.info.firstName} ${passenger.info.lastName}`.trim())
    }
    if (passenger.info.countryCode && passenger.info.areaCode && passenger.info.phoneNumber) {
      setValue('mobilePhoneNumber', {
        countryCode: passenger.info.countryCode,
        areaCode: passenger.info.areaCode,
        phoneNumber: passenger.info.phoneNumber,
      })
    }
  }

  const copyContactInfo = async () => {
    const selected = getCountryByCode(watchedValues?.mobilePhoneNumber?.countryCode || DEFAULT_COUNTRY_CODE)
    const contactInfo = {
      fullName: watchedValues.fullName || null,
      email: watchedValues.email || null,
      mobilePhoneNumber: {
        countryCode: watchedValues.mobilePhoneNumber?.countryCode || DEFAULT_COUNTRY_CODE,
        areaCode: watchedValues.mobilePhoneNumber?.areaCode || null,
        phoneNumber: watchedValues.mobilePhoneNumber?.phoneNumber || null,
        formatted: `+${selected.accessCode} ${watchedValues.mobilePhoneNumber?.areaCode || ''} ${formatPhoneNumber(watchedValues.mobilePhoneNumber?.phoneNumber || '')}`,
      },
      ...(watchedValues.passengerIndex !== undefined && { passengerIndex: watchedValues.passengerIndex }),
    }
    try {
      await navigator.clipboard.writeText(JSON.stringify(contactInfo, null, 2))
      setCopied(true)
      toast.success('Contact information copied as JSON!')
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      toast.error('Failed to copy contact information')
    }
  }

  const isValidContact = () => validateCompleteContact(watchedValues, passengers)

  const filteredCountries = useMemo(() => {
    const q = searchValue.toLowerCase()
    return countryOptions.filter((country) =>
      country.name.toLowerCase().includes(q) || country.accessCode.includes(searchValue) || country.value.toLowerCase().includes(q)
    )
  }, [countryOptions, searchValue])

  const selectedCountry = useMemo(
    () => getCountryByCode(watchedValues?.mobilePhoneNumber?.countryCode || DEFAULT_COUNTRY_CODE),
    [watchedValues?.mobilePhoneNumber?.countryCode]
  )

  const suggestedAssociated = contactType === 'unassociated' && hasPassengersWithContact

  return {
    control,
    errors,
    setValue,
    watchedValues,
    copied,
    copyContactInfo,
    isValidContact,
    contactType,
    handleContactTypeChange,
    selectedPassengerIndex,
    handlePassengerSelection,
    passengersWithContact,
    suggestedAssociated,
    filteredCountries,
    selectedCountry,
    countryCodeOpen,
    setCountryCodeOpen,
    searchValue,
    setSearchValue,
    expandedContact,
    setExpandedContact,
  }
}


