import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Controller } from 'react-hook-form'
import { Phone, Mail, User, Users, ChevronDown, ChevronUp, Check, BadgeInfo, FileCode } from 'lucide-react'
import { cn } from '@/lib/client/utils'
import useContactDetails from './useContactDetails'

function ContactHeader({ isValid, onCopy, copied, showDetails, onToggleDetails }) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Phone className="h-5 w-5 text-primary" />
        <span className="font-medium">Contact Information</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <BadgeInfo className="h-4 w-4 text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Contact information is required for PNR creation.</p>
              <p>You can use adult/child passenger data or enter manually.</p>
              <p className="text-xs text-muted-foreground">Note: Infant passengers cannot be contact persons.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Badge variant={isValid ? 'default' : 'destructive'} className="ml-2">
          {isValid ? 'Complete' : 'Required'}
        </Badge>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={onCopy} disabled={!isValid} className="gap-2">
          {copied ? (
            <>
              <Check className="h-4 w-4 text-green-500" />
              Copied
            </>
          ) : (
            <>
              <FileCode className="h-4 w-4" />
              Copy JSON
            </>
          )}
        </Button>
        <Button variant="outline" size="sm" onClick={onToggleDetails} className="gap-2">
          {showDetails ? (
            <>
              Contact <ChevronUp className="h-4 w-4" />
            </>
          ) : (
            <>
              Contact <ChevronDown className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  )
}

function ContactTypeSelector({ contactType, onChange, passengersWithContact, suggested }) {
  const hasPassengersWithContact = passengersWithContact.length > 0
  return (
    <div className="space-y-4">
      <Label className="text-base font-medium">Contact Source</Label>
      <RadioGroup value={contactType} onValueChange={onChange} className="space-y-3">
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="unassociated" id="unassociated" />
          <Label htmlFor="unassociated" className="flex items-center gap-2 cursor-pointer">
            <User className="h-4 w-4" />
            Manual Entry (Independent Contact)
          </Label>
        </div>
        {hasPassengersWithContact && (
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="associated" id="associated" />
            <Label htmlFor="associated" className="flex items-center gap-2 cursor-pointer">
              <Users className="h-4 w-4" />
              Use Passenger Information
              <Badge variant="secondary" className="text-xs">{passengersWithContact.length} Available</Badge>
              {suggested && <Badge variant="default" className="text-xs bg-blue-500 hover:bg-blue-600 animate-pulse">Suggested</Badge>}
            </Label>
          </div>
        )}
      </RadioGroup>
    </div>
  )
}

function PassengerSelector({ passengers, selectedIndex, onSelect, passengersWithContact }) {
  if (passengersWithContact.length === 0) return null
  return (
    <div className="space-y-2">
      <Label>Select Passenger</Label>
      <Select value={String(selectedIndex)} onValueChange={(value) => onSelect(parseInt(value))}>
        <SelectTrigger>
          <SelectValue placeholder="Select a passenger" />
        </SelectTrigger>
        <SelectContent>
          {passengersWithContact.map((passenger, index) => (
            <SelectItem key={index} value={String(index)}>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>
                  {passenger.info.firstName || passenger.info.lastName
                    ? `${passenger.info.firstName} ${passenger.info.lastName}`.trim()
                    : `${passenger.type} ${passengers.indexOf(passenger) + 1}`}
                </span>
                {passenger.info.email && (
                  <Badge variant="outline" className="text-xs">{passenger.info.email}</Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

function ContactForm({ control, errors, filteredCountries, selectedCountry, setValue, watchedValues, isValidContact, countryCodeOpen, setCountryCodeOpen, searchValue, setSearchValue }) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        <div className="space-y-2">
          <Label htmlFor="fullName">Full Name *</Label>
          <Controller
            name="fullName"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                id="fullName"
                placeholder="Enter full name"
                className={cn(
                  errors.fullName ? 'border-red-300 focus:border-red-500' : field.value ? 'border-green-300 focus:border-green-500' : ''
                )}
              />
            )}
          />
          {errors.fullName && <p className="text-sm text-red-500">{errors.fullName.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email Address *</Label>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                id="email"
                type="email"
                placeholder="Enter email address"
                className={cn(
                  errors.email ? 'border-red-300 focus:border-red-500' : field.value && !errors.email ? 'border-green-300 focus:border-green-500' : ''
                )}
              />
            )}
          />
          {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
        </div>

        <div className="space-y-2">
          <Label>Mobile Phone Number *</Label>
          <div className="grid grid-cols-3 gap-2">
            <div className="space-y-1">
              <Label htmlFor="countryCode" className="text-xs text-muted-foreground">Country</Label>
              <Popover open={countryCodeOpen} onOpenChange={setCountryCodeOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" role="combobox" aria-expanded={countryCodeOpen} className="w-full justify-between h-10 px-3">
                    <div className="flex items-center gap-2 truncate">
                      <span className="text-lg">{selectedCountry.flag}</span>
                      <span className="text-xs">+{selectedCountry.accessCode}</span>
                    </div>
                    <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="start">
                  <Command>
                    <CommandInput placeholder="Search country..." value={searchValue} onValueChange={setSearchValue} />
                    <CommandList>
                      <CommandEmpty>No country found.</CommandEmpty>
                      <CommandGroup>
                        {filteredCountries.map((country) => (
                          <CommandItem
                            key={country.value}
                            value={country.value}
                            onSelect={() => {
                              setValue('mobilePhoneNumber.countryCode', country.value)
                              setCountryCodeOpen(false)
                              setSearchValue('')
                            }}
                          >
                            <div className="flex items-center gap-3 w-full">
                              <span className="text-lg">{country.flag}</span>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium truncate">{country.name}</div>
                                <div className="text-sm text-muted-foreground">+{country.accessCode} ({country.value})</div>
                              </div>
                              {watchedValues?.mobilePhoneNumber?.countryCode === country.value && (
                                <Check className="h-4 w-4 text-primary" />
                              )}
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-1">
              <Label htmlFor="areaCode" className="text-xs text-muted-foreground">Area Code</Label>
              <Controller
                name="mobilePhoneNumber.areaCode"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="areaCode"
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 3)
                      field.onChange(value)
                    }}
                    placeholder="541"
                    maxLength={3}
                    className={cn(
                      errors.mobilePhoneNumber?.areaCode
                        ? 'border-red-300 focus:border-red-500'
                        : field.value && !errors.mobilePhoneNumber?.areaCode
                        ? 'border-green-300 focus:border-green-500'
                        : ''
                    )}
                  />
                )}
              />
              {errors.mobilePhoneNumber?.areaCode && (
                <p className="text-sm text-red-500">{errors.mobilePhoneNumber.areaCode.message}</p>
              )}
            </div>

            <div className="space-y-1">
              <Label htmlFor="phoneNumber" className="text-xs text-muted-foreground">Phone Number</Label>
              <Controller
                name="mobilePhoneNumber.phoneNumber"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="phoneNumber"
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 10)
                      field.onChange(value)
                    }}
                    placeholder="1234567"
                    maxLength={10}
                    className={cn(
                      errors.mobilePhoneNumber?.phoneNumber
                        ? 'border-red-300 focus:border-red-500'
                        : field.value && !errors.mobilePhoneNumber?.phoneNumber
                        ? 'border-green-300 focus:border-green-500'
                        : ''
                    )}
                  />
                )}
              />
              {errors.mobilePhoneNumber?.phoneNumber && (
                <p className="text-sm text-red-500">{errors.mobilePhoneNumber.phoneNumber.message}</p>
              )}
            </div>
          </div>

          <div className="text-sm space-y-1">
            {isValidContact() && (
              <p className="text-green-600 flex items-center gap-1">
                <Check className="h-4 w-4" />
                Complete: +{selectedCountry.accessCode} {watchedValues.mobilePhoneNumber?.areaCode}{' '}
                {watchedValues.mobilePhoneNumber?.phoneNumber}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

function ContactSummary({ visible, watchedValues, contactType, selectedCountry }) {
  if (!visible) return null
  return (
    <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
      <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Contact Summary</h4>
      <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
        <div className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span>{watchedValues.fullName}</span>
        </div>
        <div className="flex items-center gap-2">
          <Mail className="h-4 w-4" />
          <span>{watchedValues.email}</span>
        </div>
        <div className="flex items-center gap-2">
          <Phone className="h-4 w-4" />
          <span>+{selectedCountry.accessCode} {watchedValues.mobilePhoneNumber?.areaCode} {watchedValues.mobilePhoneNumber?.phoneNumber}</span>
        </div>
        {contactType === 'associated' && watchedValues.passengerIndex !== undefined && (
          <div className="flex items-center gap-2 pt-2 border-t border-green-200 dark:border-green-700">
            <Users className="h-4 w-4" />
            <span className="text-xs">Associated with passenger {watchedValues.passengerIndex + 1}</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default function ContactDetails({ passengers = [], contact, onContactChange, showDetails, onToggleDetails }) {
  const hook = useContactDetails({ passengers, contact, onContactChange })
  const {
    control,
    errors,
    isValidContact,
    copyContactInfo,
    copied,
    contactType,
    handleContactTypeChange,
    selectedPassengerIndex,
    handlePassengerSelection,
    passengersWithContact,
    suggestedAssociated,
    filteredCountries,
    selectedCountry,
    setValue,
    watchedValues,
    countryCodeOpen,
    setCountryCodeOpen,
    searchValue,
    setSearchValue,
  } = hook

  return (
    <div className="space-y-4">
      <ContactHeader isValid={isValidContact()} onCopy={copyContactInfo} copied={copied} showDetails={showDetails} onToggleDetails={() => { if (!showDetails) hook.setExpandedContact(true); onToggleDetails() }} />

      {showDetails && (
        <Card className={cn('transition-all duration-200', hook.expandedContact ? 'ring-2 ring-primary' : '')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Contact Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <ContactTypeSelector
              contactType={contactType}
              onChange={handleContactTypeChange}
              passengersWithContact={passengersWithContact}
              suggested={suggestedAssociated}
            />

            {contactType === 'associated' && passengersWithContact.length > 0 && (
              <PassengerSelector
                passengers={passengers}
                selectedIndex={selectedPassengerIndex}
                onSelect={handlePassengerSelection}
                passengersWithContact={passengersWithContact}
              />
            )}

            <ContactForm
              control={control}
              errors={errors}
              filteredCountries={filteredCountries}
              selectedCountry={selectedCountry}
              setValue={setValue}
              watchedValues={watchedValues}
              isValidContact={isValidContact}
              countryCodeOpen={hook.countryCodeOpen}
              setCountryCodeOpen={hook.setCountryCodeOpen}
              searchValue={hook.searchValue}
              setSearchValue={hook.setSearchValue}
            />

            <ContactSummary
              visible={isValidContact()}
              watchedValues={watchedValues}
              contactType={contactType}
              selectedCountry={selectedCountry}
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}


