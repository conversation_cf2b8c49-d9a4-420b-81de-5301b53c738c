import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { ContactDetails } from '../../ContactDetails'

jest.mock('sonner', () => ({ toast: { success: jest.fn(), error: jest.fn() } }))

jest.mock('@/lib/client/constants/countries', () => ({
  getCountryOptions: () => [
    { value: 'TR', name: 'Turkey', flag: '🇹🇷', accessCode: '90' },
    { value: 'US', name: 'United States', flag: '🇺🇸', accessCode: '1' },
  ],
  getCountryByCode: (code) => ({ TR: { value: 'TR', accessCode: '90', flag: '🇹🇷' }, US: { value: 'US', accessCode: '1', flag: '🇺🇸' } })[code] || { value: 'TR', accessCode: '90', flag: '🇹🇷' },
  DEFAULT_COUNTRY_CODE: 'TR',
  DEFAULT_AREA_CODE: '541',
}), { virtual: true })

jest.mock('@/lib/client/validations/contact', () => ({
  contactSchema: { _def: {} }, // dummy for zodResolver
  validateCompleteContact: (values) => Boolean(values?.fullName && values?.email && values?.mobilePhoneNumber?.phoneNumber),
  formatPhoneNumber: (p) => p,
  canPassengerBeContact: (p) => p && p.type && p.type.toLowerCase() !== 'infant',
}), { virtual: true })

jest.mock('@hookform/resolvers/zod', () => ({ zodResolver: () => (values) => ({ values, errors: {} }) }))

jest.mock('@/lib/client/utils', () => ({ cn: (...args) => args.filter(Boolean).join(' ') }), { virtual: true })

// UI stubs
jest.mock('@/components/ui/button', () => ({ Button: (props) => <button {...props} /> }), { virtual: true })
jest.mock('@/components/ui/badge', () => ({ Badge: ({ children, ...p }) => <span {...p}>{children}</span> }), { virtual: true })
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...p }) => <div {...p}>{children}</div>,
  CardContent: ({ children, ...p }) => <div {...p}>{children}</div>,
  CardHeader: ({ children, ...p }) => <div {...p}>{children}</div>,
  CardTitle: ({ children, ...p }) => <div {...p}>{children}</div>,
}), { virtual: true })
jest.mock('@/components/ui/input', () => ({ Input: (props) => <input {...props} /> }), { virtual: true })
jest.mock('@/components/ui/label', () => ({ Label: ({ children, htmlFor, ...p }) => <label htmlFor={htmlFor} {...p}>{children}</label> }), { virtual: true })
jest.mock('@/components/ui/select', () => ({
  Select: ({ children }) => <div>{children}</div>,
  SelectTrigger: ({ children }) => <div>{children}</div>,
  SelectValue: ({ children }) => <span>{children}</span>,
  SelectContent: ({ children }) => <div>{children}</div>,
  SelectItem: ({ children }) => <div role="option">{children}</div>,
}), { virtual: true })
jest.mock('@/components/ui/radio-group', () => ({
  RadioGroup: ({ children }) => <div>{children}</div>,
  RadioGroupItem: (props) => <input type="radio" {...props} />,
}), { virtual: true })
jest.mock('@/components/ui/popover', () => ({
  Popover: ({ children }) => <div>{children}</div>,
  PopoverTrigger: ({ children }) => <div>{children}</div>,
  PopoverContent: ({ children }) => <div>{children}</div>,
}), { virtual: true })
jest.mock('@/components/ui/command', () => ({
  Command: ({ children }) => <div>{children}</div>,
  CommandInput: (props) => <input {...props} />,
  CommandList: ({ children }) => <div>{children}</div>,
  CommandEmpty: ({ children }) => <div>{children}</div>,
  CommandGroup: ({ children }) => <div>{children}</div>,
  CommandItem: ({ children, onSelect }) => <div onClick={onSelect}>{children}</div>,
}), { virtual: true })
jest.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }) => <div>{children}</div>,
  Tooltip: ({ children }) => <div>{children}</div>,
  TooltipTrigger: ({ children }) => <div>{children}</div>,
  TooltipContent: ({ children }) => <div>{children}</div>,
}), { virtual: true })

describe('ContactDetails', () => {
  const basePassengers = [
    { type: 'ADULT', info: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', countryCode: 'TR', areaCode: '541', phoneNumber: '1234567' } },
    { type: 'INFANT', info: {} },
  ]

  function setup(props = {}) {
    const onContactChange = jest.fn()
    const utils = render(
      <ContactDetails
        passengers={basePassengers}
        contact={props.contact}
        onContactChange={onContactChange}
        showDetails={true}
        onToggleDetails={() => {}}
      />
    )
    return { onContactChange, ...utils }
  }

  it('renders header and badges', () => {
    setup()
    expect(screen.getByText('Contact Information')).toBeInTheDocument()
    expect(screen.getByText('Required')).toBeInTheDocument()
    expect(screen.getByText('Contact Details')).toBeInTheDocument()
  })

  it('suggests associated contact when passengers have contact info', () => {
    setup()
    expect(screen.getByText(/Use Passenger Information/)).toBeInTheDocument()
  })

  it('allows entering manual contact and shows Complete badge when valid', () => {
    setup()
    fireEvent.change(screen.getByLabelText('Full Name *'), { target: { value: 'Jane Doe' } })
    fireEvent.change(screen.getByLabelText('Email Address *'), { target: { value: '<EMAIL>' } })
    const phoneInput = screen.getByLabelText('Phone Number')
    fireEvent.change(phoneInput, { target: { value: '1234567' } })
    expect(screen.getByText('Complete')).toBeInTheDocument()
  })

  it('shows passenger selector when associated selected', () => {
    setup()
    // Auto-suggest switches to associated in hook when passengers have contact info
    expect(screen.getByText('Select Passenger')).toBeInTheDocument()
  })
})


