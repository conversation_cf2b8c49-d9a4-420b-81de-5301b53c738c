import React from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'

export default function PNRDetails({ pnr, onNewPNR }) {
  return (
    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>PNR Created Successfully</CardTitle>
          <CardDescription className="mt-3">Pnr Number: {pnr?.pnrNumber}</CardDescription>
        </div>
        <Button variant="outline" size="sm" onClick={onNewPNR}>New PNR</Button>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex justify-between"><span>PNR:</span><span>{pnr?.pnrNumber}</span></div>
          <div className="flex justify-between"><span>Surname:</span><span>{pnr?.surname}</span></div>
        </div>
      </CardContent>
    </Card>
  )
}


