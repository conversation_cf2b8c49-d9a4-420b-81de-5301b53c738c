import { useState } from 'react'
import { validateCompleteContact } from '@/lib/client/validations/contact'

export default function usePNRCreation({ onPNRCreated }) {
  const [step, setStep] = useState(1)
  const [pnrType, setPnrType] = useState('CASH_TICKET')
  const [channel, setChannel] = useState('DEFAULT')
  const [passengers, setPassengers] = useState({ ADULT: 1 })
  const [passengerInfo, setPassengerInfo] = useState([
    { key: 'p1', type: 'ADULT', info: { title: '', firstName: '', lastName: '', dob: '', passport: '' } },
  ])
  const [contact, setContact] = useState(null)
  const [serviceRequests, setServiceRequests] = useState({})
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null)

  const toPassengersStep = () => setStep(2)
  const toServicesStep = () => setStep(3)
  const toPaymentStep = () => setStep(4)
  const backToFlights = () => setStep(1)
  const backToPassengers = () => setStep(2)

  const canContinueFromPassengers = () => validateCompleteContact(contact)

  const createPNR = () => {
    const mockPNR = {
      pnrNumber: 'ABC123',
      surname: passengerInfo[0]?.info?.lastName || 'DOE',
      passengers: passengerInfo,
      flights: [],
      troyaText: 'TROYA MOCK\nPNR ABC123',
    }
    onPNRCreated?.(mockPNR)
  }

  return {
    // state
    step, pnrType, channel, passengers, passengerInfo, contact, serviceRequests, selectedPaymentMethod,
    // setters
    setStep, setPnrType, setChannel, setPassengers, setPassengerInfo, setContact, setServiceRequests, setSelectedPaymentMethod,
    // nav
    toPassengersStep, toServicesStep, toPaymentStep, backToFlights, backToPassengers,
    // actions
    canContinueFromPassengers, createPNR,
  }
}


