import React from 'react'
import { Card, CardContent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { PassengerDetails } from '@/components/pnr/PassengerDetails'
import { ContactDetails } from '@/components/pnr/ContactDetails'
import { ChannelSelect } from '@/components/pnr/ChannelSelect'
import usePNRCreation from './usePNRCreation'

function Step1({ hook }) {
  return (
    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
      <CardHeader>
        <CardTitle>Flight Details</CardTitle>
        <CardDescription>Enter your flight preferences</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-6">
          <ChannelSelect channel={hook.channel} onChannelSelect={hook.setChannel} />
          <div>
            <div className="text-sm text-muted-foreground mb-2">Booking Type</div>
            <div className="flex gap-2 flex-wrap">
              {['CASH_TICKET','AWARD_TICKET_MILES_TAX','AWARD_TICKET_CASH_TAX','FREE_RESERVATION','PRICED_RESERVATION'].map((t) => (
                <Button key={t} variant={hook.pnrType === t ? undefined : 'outline'} onClick={() => hook.setPnrType(t)}>{t}</Button>
              ))}
            </div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground mb-2">Passengers</div>
            <div className="flex gap-2 items-center">
              <span>Adults:</span>
              <Button variant="outline" onClick={() => hook.setPassengers((p) => ({ ...p, ADULT: Math.max(1, (p.ADULT||1)-1) }))}>-</Button>
              <span>{hook.passengers.ADULT || 1}</span>
              <Button variant="outline" onClick={() => hook.setPassengers((p) => ({ ...p, ADULT: (p.ADULT||1)+1 }))}>+</Button>
            </div>
          </div>
        </div>
        <Button className="w-full" onClick={hook.toPassengersStep}>Continue to Passenger Information</Button>
      </CardContent>
    </Card>
  )
}

function Step2({ hook }) {
  return (
    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
      <CardHeader>
        <CardTitle>Passenger Information</CardTitle>
        <CardDescription>Enter details for all passengers</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <PassengerDetails
          passengers={hook.passengerInfo}
          onPassengersChange={hook.setPassengerInfo}
          totalPassengers={hook.passengers}
          showDetails={true}
          onToggleDetails={() => {}}
          pnrType={hook.pnrType}
        />
        <ContactDetails
          passengers={hook.passengerInfo}
          contact={hook.contact}
          onContactChange={hook.setContact}
          showDetails={true}
          onToggleDetails={() => {}}
        />
        <div className="flex gap-3">
          <Button variant="outline" onClick={hook.backToFlights} className="w-24">Back</Button>
          <Button className="flex-1" onClick={hook.toServicesStep} disabled={!hook.canContinueFromPassengers()}>Add Additional Services</Button>
          <Button className="flex-1" onClick={hook.toPaymentStep} disabled={!hook.canContinueFromPassengers()}>Jump To Payment</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function Step3({ hook }) {
  return (
    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
      <CardHeader>
        <CardTitle>Additional Services</CardTitle>
        <CardDescription>Select additional services</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-sm text-muted-foreground">(Mock) No additional services available.</div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={hook.backToPassengers}>Back</Button>
          <Button onClick={hook.toPaymentStep}>Continue</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function Step4({ hook }) {
  return (
    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700">
      <CardHeader>
        <CardTitle>Payment</CardTitle>
        <CardDescription>Select your preferred payment method</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex gap-2">
          {['CREDIT_CARD','VOUCHER'].map((m) => (
            <Button key={m} variant={hook.selectedPaymentMethod === m ? undefined : 'outline'} onClick={() => hook.setSelectedPaymentMethod(m)}>{m}</Button>
          ))}
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={() => hook.setStep(3)}>Back</Button>
          <Button className="flex-1" onClick={hook.createPNR} disabled={!hook.selectedPaymentMethod || !hook.channel}>Create PNR</Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default function PNRCreation({ onPNRCreated }) {
  const hook = usePNRCreation({ onPNRCreated })
  return (
    <div className="space-y-6">
      {hook.step === 1 && <Step1 hook={hook} />}
      {hook.step === 2 && <Step2 hook={hook} />}
      {hook.step === 3 && <Step3 hook={hook} />}
      {hook.step === 4 && <Step4 hook={hook} />}
    </div>
  )
}


