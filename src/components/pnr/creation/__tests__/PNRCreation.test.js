import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { PNRCreation } from '../../PNRCreation'

jest.mock('lucide-react', () => new Proxy({}, { get: () => () => null }))

jest.mock('@/components/ui/button', () => ({ Button: (p) => <button {...p} /> }), { virtual: true })
jest.mock('@/components/ui/card', () => ({ Card: (p) => <div {...p} />, CardContent: (p) => <div {...p} />, CardHeader: (p) => <div {...p} />, CardTitle: (p) => <div {...p} />, CardDescription: (p) => <div {...p} /> }), { virtual: true })

jest.mock('@/components/pnr/PassengerDetails', () => ({ PassengerDetails: () => <div>Passenger Details Component</div> }), { virtual: true })
jest.mock('@/components/pnr/ContactDetails', () => ({ ContactDetails: () => <div>Contact Details Component</div> }), { virtual: true })

jest.mock('@/lib/client/validations/contact', () => ({ validateCompleteContact: () => true }), { virtual: true })

describe('PNRCreation flow', () => {
  it('navigates through steps and calls onPNRCreated', () => {
    const onPNRCreated = jest.fn()
    render(<PNRCreation onPNRCreated={onPNRCreated} />)

    // Step 1
    expect(screen.getByText('Flight Details')).toBeInTheDocument()
    fireEvent.click(screen.getByText('Continue to Passenger Information'))

    // Step 2
    expect(screen.getByText('Passenger Information')).toBeInTheDocument()
    fireEvent.click(screen.getByText('Jump To Payment'))

    // Step 4
    expect(screen.getByText('Payment')).toBeInTheDocument()
    fireEvent.click(screen.getByText('CREDIT_CARD'))
    fireEvent.click(screen.getByText('Create PNR'))
    expect(onPNRCreated).toHaveBeenCalled()
  })
})


