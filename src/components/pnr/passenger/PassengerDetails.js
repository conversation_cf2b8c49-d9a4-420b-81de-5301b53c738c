import React from 'react'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { DEFAULT_COUNTRY_CODE, getCountryByCode, getCountryOptions } from '@/lib/client/constants/countries'
import { PASSENGER_TITLES } from '@/lib/client/constants/passengers'
import { cn } from '@/lib/client/utils'
import { canEditPassengerDetails, isMsReqPassengerType, validateEmail, validatePhoneNumber, validateAreaCode, hasCompleteContactInfo } from '@/lib/client/validations/passenger'
import { Accessibility, Baby, BadgeInfo, ChevronDown, ChevronUp, Mail, Phone, Hammer, User, Users, Check, AlertCircle } from 'lucide-react'
import usePassengerDetails from './usePassengerDetails'

function Header({ passengers, totalCount, showDetails, onToggleDetails }) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Users className="h-5 w-5 text-primary" />
        <span className="font-medium">Passenger Details (Optional)</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div>
                <BadgeInfo className="h-4 w-4 text-muted-foreground" />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>All passenger details are optional.</p>
              <p>Fill in only what you need.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Badge variant="outline" className="ml-2">{passengers?.length || 0}/{totalCount}</Badge>
      </div>
      <Button variant="outline" size="sm" onClick={onToggleDetails} className="gap-2">
        {showDetails ? (<><span>Details</span> <ChevronUp className="h-4 w-4" /></>) : (<><span>Details</span> <ChevronDown className="h-4 w-4" /></>)}
      </Button>
    </div>
  )
}

function PassengerIcon({ type }) {
  const map = {
    DISABLED: Accessibility,
    LABOR: Hammer,
    INFANT: Baby,
    ETHNIC_INFANT: Baby,
    LABOR_INFANT: Baby,
    CHILD: Users,
    ETHNIC_CHILD: Users,
    LABOR_CHILD: Users,
  }
  const Icon = map[type] || User
  return <Icon className="h-5 w-5 text-muted-foreground" />
}

function TitleSelect({ passenger, disabled, onChange }) {
  return (
    <div className="space-y-1">
      <Label>Title</Label>
      <Select value={passenger.info.title} onValueChange={(v) => onChange('title', v)} disabled={disabled}>
        <SelectTrigger>
          <SelectValue placeholder="Select title" />
        </SelectTrigger>
        <SelectContent>
          {PASSENGER_TITLES.map((t) => (
            <SelectItem key={t.value} value={t.value}>{t.label}</SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

function IdentityFields({ passenger, disabled, onChange }) {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-1">
        <Label>First Name</Label>
        <Input value={passenger.info.firstName} onChange={(e) => onChange('firstName', e.target.value)} placeholder="Enter first name" disabled={disabled} />
      </div>
      <div className="space-y-1">
        <Label>Last Name</Label>
        <Input value={passenger.info.lastName} onChange={(e) => onChange('lastName', e.target.value)} placeholder="Enter last name" disabled={disabled} />
      </div>
      <div className="space-y-1">
        <Label>Date of Birth</Label>
        <Input type="date" value={passenger.info.dob} onChange={(e) => onChange('dob', e.target.value)} disabled={disabled} />
      </div>
      <div className="space-y-1">
        <Label>Passport Number</Label>
        <Input value={passenger.info.passport} onChange={(e) => onChange('passport', e.target.value)} placeholder="Enter passport number" disabled={disabled} />
      </div>
    </div>
  )
}

function ContactFields({ passenger, onChange, disabled }) {
  const country = getCountryByCode(passenger.info.countryCode || DEFAULT_COUNTRY_CODE)
  return (
    <div className="space-y-2">
      <Label>Mobile Phone (Optional)</Label>
      <div className="grid grid-cols-3 gap-2">
        <div className="space-y-1">
          <Label className="text-xs text-muted-foreground">Country</Label>
          <Select value={passenger.info.countryCode || DEFAULT_COUNTRY_CODE} onValueChange={(v) => onChange('countryCode', v)} disabled={disabled}>
            <SelectTrigger className="h-9">
              <SelectValue>
                <div className="flex items-center gap-1">
                  <span>{country.flag}</span>
                  <span className="text-xs">+{country.accessCode}</span>
                </div>
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {getCountryOptions().slice(0, 20).map((c) => (
                <SelectItem key={c.value} value={c.value}>
                  <div className="flex items-center gap-2">
                    <span>{c.flag}</span>
                    <span className="text-xs">+{c.accessCode}</span>
                    <span className="text-xs truncate">{c.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-1">
          <Label className="text-xs text-muted-foreground">Area Code</Label>
          <Input
            value={passenger.info.areaCode || ''}
            onChange={(e) => onChange('areaCode', e.target.value.replace(/\D/g, '').slice(0, 3))}
            placeholder="541"
            maxLength={3}
            className={cn('h-9', passenger.info.areaCode && !validateAreaCode(passenger.info.areaCode) ? 'border-red-300 focus:border-red-500' : passenger.info.areaCode && validateAreaCode(passenger.info.areaCode) ? 'border-green-300 focus:border-green-500' : 'h-9')}
            disabled={disabled}
          />
          {passenger.info.areaCode && !validateAreaCode(passenger.info.areaCode) && (
            <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-2 w-2" />3 digits</p>
          )}
        </div>
        <div className="space-y-1">
          <Label className="text-xs text-muted-foreground">Phone</Label>
          <Input
            value={passenger.info.phoneNumber || ''}
            onChange={(e) => onChange('phoneNumber', e.target.value.replace(/\D/g, '').slice(0, 10))}
            placeholder="1234567"
            maxLength={10}
            className={cn('h-9', passenger.info.phoneNumber && !validatePhoneNumber(passenger.info.phoneNumber) ? 'border-red-300 focus:border-red-500' : passenger.info.phoneNumber && validatePhoneNumber(passenger.info.phoneNumber) ? 'border-green-300 focus:border-green-500' : 'h-9')}
            disabled={disabled}
          />
          {passenger.info.phoneNumber && !validatePhoneNumber(passenger.info.phoneNumber) && (
            <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-2 w-2" />6-10 digits</p>
          )}
        </div>
      </div>
      {passenger.info.areaCode && passenger.info.phoneNumber && validateAreaCode(passenger.info.areaCode) && validatePhoneNumber(passenger.info.phoneNumber) && (
        <p className="text-sm text-green-600 flex items-center gap-1"><Check className="h-4 w-4" />Complete: +{country.accessCode} {passenger.info.areaCode} {passenger.info.phoneNumber}</p>
      )}
    </div>
  )
}

function EmailField({ passenger, onChange, disabled }) {
  const value = passenger.info.email || ''
  const valid = value ? validateEmail(value) : true
  return (
    <div className="space-y-1">
      <Label>Email Address (Optional)</Label>
      <Input
        type="email"
        value={value}
        onChange={(e) => onChange('email', e.target.value)}
        placeholder="Enter email address"
        className={cn(value && !valid ? 'border-red-300 focus:border-red-500' : value && valid ? 'border-green-300 focus:border-green-500' : '')}
        disabled={disabled}
      />
      {value && !valid && (<p className="text-sm text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />Please enter a valid email address</p>)}
      {value && valid && (<p className="text-sm text-green-600 flex items-center gap-1"><Check className="h-3 w-3" />Valid email address</p>)}
    </div>
  )
}

function PassengerCard({ index, passenger, isExpanded, onToggle, onChange, pnrType }) {
  const disabled = !canEditPassengerDetails(passenger)
  const nameLabel = passenger.info.firstName || passenger.info.lastName
    ? `${passenger.info.firstName} ${passenger.info.lastName}`.trim()
    : (
      <>
        {passenger.type.charAt(0).toUpperCase() + passenger.type.slice(1)} {index + 1}
        <Badge variant="secondary" className="ml-2 text-xs">Optional Details</Badge>
      </>
    )
  return (
    <Card className={cn('transition-all duration-200', isExpanded ? 'ring-2 ring-primary' : '')}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between cursor-pointer" onClick={onToggle}>
          <div className="flex items-center gap-3">
            <PassengerIcon type={passenger.type} />
            <span className="font-medium">{nameLabel}</span>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              {passenger.info.passport && (<span>{passenger.info.passport}</span>)}
              {passenger.info.email && (
                <Badge variant={validateEmail(passenger.info.email) ? 'outline' : 'destructive'} className="text-xs">
                  <Mail className="h-3 w-3 mr-1" />{passenger.info.email}
                  {!validateEmail(passenger.info.email) && <AlertCircle className="h-3 w-3 ml-1" />}
                </Badge>
              )}
              {passenger.info.countryCode && passenger.info.areaCode && passenger.info.phoneNumber && (
                <Badge variant={validateAreaCode(passenger.info.areaCode) && validatePhoneNumber(passenger.info.phoneNumber) ? 'outline' : 'destructive'} className="text-xs">
                  <Phone className="h-3 w-3 mr-1" />+{getCountryByCode(passenger.info.countryCode).accessCode} {passenger.info.areaCode} {passenger.info.phoneNumber}
                  {(!validateAreaCode(passenger.info.areaCode) || !validatePhoneNumber(passenger.info.phoneNumber)) && <AlertCircle className="h-3 w-3 ml-1" />}
                </Badge>
              )}
              {hasCompleteContactInfo(passenger) && passenger.type !== 'infant' && (
                <Badge variant="default" className="text-xs bg-green-500 hover:bg-green-600"><Check className="h-3 w-3 mr-1" />Contact Ready</Badge>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">{isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}</div>
        </div>
        {isExpanded && (
          <div className="mt-4 space-y-4">
            {passenger.type !== 'infant' && (
              <div className="space-y-1">
                <Label>Membership Number</Label>
                <Input value={passenger.info.membershipNumber} onChange={(e) => onChange('membershipNumber', e.target.value)} placeholder="Enter Miles&Smiles number" maxLength={9} pattern="[0-9]*" />
                {index === 0 && pnrType?.startsWith('AWARD_TICKET') && (
                  <div className="flex p-2 space-y-1"><div className="flex-initial"><p className="text-xs text-muted-foreground text-red-300">Membership number is required</p></div></div>
                )}
                {!isMsReqPassengerType(passenger) && index !== 0 && pnrType?.startsWith('AWARD_TICKET') && (
                  <div className="flex p-2 space-y-1"><div className="flex-initial"><p className="text-xs text-muted-foreground">Optional - Enter 9-digit Miles&Smiles number</p></div></div>
                )}
                {isMsReqPassengerType(passenger) && (
                  <div className="flex p-2 space-y-1"><div className="flex-initial"><p className="text-xs text-muted-foreground">Optional - Enter 9-digit Miles&Smiles number for Discount Eligibility</p></div></div>
                )}
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <TitleSelect passenger={passenger} disabled={disabled} onChange={(f, v) => onChange(f, v)} />
              <div />
            </div>
            <IdentityFields passenger={passenger} disabled={disabled} onChange={(f, v) => onChange(f, v)} />
            {passenger.type !== 'infant' && (
              <>
                <EmailField passenger={passenger} disabled={disabled} onChange={(f, v) => onChange(f, v)} />
                <ContactFields passenger={passenger} disabled={disabled} onChange={(f, v) => onChange(f, v)} />
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default function PassengerDetails({ passengers, onPassengersChange, totalPassengers, showDetails, onToggleDetails, pnrType }) {
  const { expandedPassenger, setExpandedPassenger, totalCount, updatePassenger } = usePassengerDetails({ passengers, onPassengersChange, totalPassengers, pnrType })
  return (
    <div className="space-y-4">
      <Header passengers={passengers} totalCount={totalCount} showDetails={showDetails} onToggleDetails={() => { if (!showDetails) setExpandedPassenger(0); onToggleDetails() }} />
      {showDetails && (
        <div className="space-y-3">
          {passengers.map((passenger, index) => (
            <PassengerCard
              key={passenger.key || index}
              index={index}
              passenger={passenger}
              isExpanded={expandedPassenger === index}
              pnrType={pnrType}
              onToggle={() => setExpandedPassenger(expandedPassenger === index ? -1 : index)}
              onChange={(field, value) => updatePassenger(index, field, value)}
            />
          ))}
        </div>
      )}
    </div>
  )
}


