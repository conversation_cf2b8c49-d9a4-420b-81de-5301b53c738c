import { useMemo, useState } from 'react'
import { PASSENGER_TYPES } from '@/lib/client/constants/passengers'
import { sanitizePassengerData } from '@/lib/client/validations/passenger'

export default function usePassengerDetails({ passengers, onPassengersChange, totalPassengers, pnrType }) {
  const [expandedPassenger, setExpandedPassenger] = useState(-1)

  const totalCount = useMemo(() => {
    if (!totalPassengers) return 0
    return Object.values(PASSENGER_TYPES).reduce((sum, type) => sum + (totalPassengers[type.code] || 0), 0)
  }, [totalPassengers])

  const updatePassenger = (index, field, value) => {
    const updated = passengers.map((p, i) => {
      if (i !== index) return p
      let copy = { ...p, info: { ...p.info, [field]: value } }
      copy = sanitizePassengerData(copy)
      return copy
    })
    onPassengersChange(updated)
  }

  return {
    expandedPassenger,
    setExpandedPassenger,
    totalCount,
    updatePassenger,
  }
}


