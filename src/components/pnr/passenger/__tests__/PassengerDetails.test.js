import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { PassengerDetails } from '../../PassengerDetails'

jest.mock('lucide-react', () => new Proxy({}, { get: () => () => null }))

jest.mock('@/components/ui/button', () => ({ Button: (p) => <button {...p} /> }), { virtual: true })
jest.mock('@/components/ui/badge', () => ({ Badge: ({ children, ...p }) => <span {...p}>{children}</span> }), { virtual: true })
jest.mock('@/components/ui/card', () => ({ Card: (p) => <div {...p} />, CardContent: (p) => <div {...p} /> }), { virtual: true })
jest.mock('@/components/ui/input', () => ({ Input: (p) => <input {...p} /> }), { virtual: true })
jest.mock('@/components/ui/label', () => ({ Label: ({ children, htmlFor, ...p }) => <label htmlFor={htmlFor} {...p}>{children}</label> }), { virtual: true })
jest.mock('@/components/ui/select', () => ({ Select: ({ children }) => <div>{children}</div>, SelectTrigger: ({ children }) => <div>{children}</div>, SelectValue: ({ children }) => <span>{children}</span>, SelectContent: ({ children }) => <div>{children}</div>, SelectItem: ({ children }) => <div role="option">{children}</div> }), { virtual: true })
jest.mock('@/components/ui/tooltip', () => ({ TooltipProvider: ({ children }) => <div>{children}</div>, Tooltip: ({ children }) => <div>{children}</div>, TooltipTrigger: ({ children }) => <div>{children}</div>, TooltipContent: ({ children }) => <div>{children}</div> }), { virtual: true })

jest.mock('@/lib/client/constants/countries', () => ({
  DEFAULT_COUNTRY_CODE: 'TR',
  getCountryByCode: () => ({ accessCode: '90', flag: '🇹🇷' }),
  getCountryOptions: () => [{ value: 'TR', name: 'Turkey', flag: '🇹🇷', accessCode: '90' }],
}), { virtual: true })

jest.mock('@/lib/client/constants/passengers', () => ({ PASSENGER_TITLES: [{ value: 'MR', label: 'Mr' }], PASSENGER_TYPES: { ADULT: { code: 'ADULT' } } }), { virtual: true })
jest.mock('@/lib/client/validations/passenger', () => ({
  canEditPassengerDetails: () => true,
  isMsReqPassengerType: () => true,
  sanitizePassengerData: (p) => p,
  validateEmail: (e) => /@/.test(String(e||'')),
  validatePhoneNumber: (p) => String(p||'').length >= 6,
  validateAreaCode: (a) => String(a||'').length === 3,
  hasCompleteContactInfo: () => true,
}), { virtual: true })

describe('PassengerDetails', () => {
  const passengers = [
    { key: '1', type: 'ADULT', info: { title: '', firstName: '', lastName: '', dob: '', passport: '' } },
  ]
  const totals = { ADULT: 1 }

  it('renders header with counts and toggles details', () => {
    const onChange = jest.fn()
    render(<PassengerDetails passengers={passengers} onPassengersChange={onChange} totalPassengers={totals} showDetails={false} onToggleDetails={() => {}} pnrType="CASH_TICKET" />)
    expect(screen.getByText('Passenger Details (Optional)')).toBeInTheDocument()
  })

  it('expands first passenger on toggle and updates fields', () => {
    const onChange = jest.fn()
    render(<PassengerDetails passengers={passengers} onPassengersChange={onChange} totalPassengers={totals} showDetails={true} onToggleDetails={() => {}} pnrType="CASH_TICKET" />)
    fireEvent.click(screen.getByText(/ADULT 1/i))
    const firstName = screen.getByPlaceholderText('Enter first name')
    fireEvent.change(firstName, { target: { value: 'Jane' } })
    expect(onChange).toHaveBeenCalled()
  })
})


