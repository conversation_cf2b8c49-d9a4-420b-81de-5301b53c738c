import { cn } from '@/lib/client/utils'

const variants = {
  default: 'bg-gray-900 text-white',
  secondary: 'bg-gray-100 text-gray-900 border border-gray-200',
  outline: 'border border-gray-300 text-gray-900',
  destructive: 'bg-red-600 text-white',
}

export function Badge({ className, variant = 'default', children, ...rest }) {
  return (
    <span
      className={cn(
        'inline-flex items-center rounded-md px-2 py-0.5 text-xs font-medium',
        variants[variant] || variants.default,
        className,
      )}
      {...rest}
    >
      {children}
    </span>
  )
}


