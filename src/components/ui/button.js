import { cn } from '@/lib/client/utils'

const variantClasses = {
  default: 'bg-[#E81932] text-white hover:bg-rose-700',
  outline: 'border border-gray-300 bg-transparent hover:bg-gray-100 text-gray-900',
  destructive: 'bg-red-600 text-white hover:bg-red-700',
  ghost: 'bg-transparent hover:bg-gray-100 text-gray-900',
  link: 'underline-offset-4 hover:underline text-[#E81932] bg-transparent',
}

const sizeClasses = {
  sm: 'h-8 px-3 text-sm',
  md: 'h-10 px-4 text-sm',
  lg: 'h-11 px-6 text-base',
}

export function Button({
  className,
  variant = 'default',
  size = 'md',
  children,
  ...rest
}) {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-300 disabled:pointer-events-none disabled:opacity-50',
        variantClasses[variant] || variantClasses.default,
        sizeClasses[size] || sizeClasses.md,
        className,
      )}
      {...rest}
    >
      {children}
    </button>
  )
}


