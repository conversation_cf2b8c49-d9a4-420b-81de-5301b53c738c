export function Card({ className, ...rest }) { return <div className={['bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-0 ring-1 ring-gray-200 dark:ring-gray-700 rounded-lg', className].filter(Boolean).join(' ')} {...rest} /> }
export function CardContent({ className, ...rest }) { return <div className={['p-4', className].filter(Boolean).join(' ')} {...rest} /> }
export function CardHeader({ className, ...rest }) { return <div className={['p-4', className].filter(Boolean).join(' ')} {...rest} /> }
export function CardTitle({ className, ...rest }) { return <div className={['text-lg font-semibold', className].filter(Boolean).join(' ')} {...rest} /> }
export function CardDescription({ className, ...rest }) { return <div className={['text-sm text-gray-500 dark:text-gray-400', className].filter(<PERSON><PERSON>an).join(' ')} {...rest} /> }


