export function Command({ children }) { return <div>{children}</div> }
export function CommandInput(props) { return <input {...props} /> }
export function CommandList({ children }) { return <div>{children}</div> }
export function CommandEmpty({ children }) { return <div>{children}</div> }
export function CommandGroup({ children }) { return <div>{children}</div> }
export function CommandItem({ children, onSelect, ...rest }) { return <div onClick={onSelect} {...rest}>{children}</div> }


