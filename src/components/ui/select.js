export function Select({ children }) { return <div className="relative inline-block w-full">{children}</div> }
export function SelectTrigger({ children, className, ...rest }) { return <div className={['h-10 px-3 border rounded-md flex items-center justify-between', className].filter(Boolean).join(' ')} {...rest}>{children}</div> }
export function SelectValue({ children }) { return <span className="truncate">{children}</span> }
export function SelectContent({ children }) { return <div className="mt-2 border rounded-md bg-white shadow p-2">{children}</div> }
export function SelectItem({ children, ...rest }) { return <div role="option" className="px-2 py-1 hover:bg-gray-100 rounded cursor-pointer" {...rest}>{children}</div> }


