export const DEFAULT_COUNTRY_CODE = 'TR'
export const DEFAULT_AREA_CODE = '541'

const COUNTRIES = [
  { value: 'TR', name: 'Turkey', flag: '🇹🇷', accessCode: '90' },
  { value: 'US', name: 'United States', flag: '🇺🇸', accessCode: '1' },
  { value: 'GB', name: 'United Kingdom', flag: '🇬🇧', accessCode: '44' },
  { value: 'DE', name: 'Germany', flag: '🇩🇪', accessCode: '49' },
]

export function getCountryOptions() {
  return COUNTRIES
}

export function getCountryByCode(code) {
  return COUNTRIES.find((c) => c.value === code) || COUNTRIES[0]
}


