export function canEditPassengerDetails(passenger) {
  const type = String(passenger?.type || '').toLowerCase()
  return type !== 'infant'
}

export function isMsReqPassengerType(passenger) {
  const type = String(passenger?.type || '').toUpperCase()
  return ['ADULT', 'CHILD'].includes(type)
}

export function sanitizePassengerData(passenger) {
  const type = String(passenger?.type || '').toLowerCase()
  if (type === 'infant') {
    return { ...passenger, info: { ...passenger.info, email: '', phoneNumber: '', areaCode: '', countryCode: '' } }
  }
  return passenger
}

export function validateEmail(email) {
  return /.+@.+\..+/.test(String(email || ''))
}

export function validatePhoneNumber(phoneNumber) {
  const s = String(phoneNumber || '')
  return s.length >= 6 && s.length <= 10
}

export function validateAreaCode(areaCode) {
  const s = String(areaCode || '')
  return s.length === 3
}

export function hasCompleteContactInfo(passenger) {
  const i = passenger?.info || {}
  return Boolean(i.email && i.countryCode && i.areaCode && i.phoneNumber)
}


