import { z } from 'zod'

export const contactSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email'),
  mobilePhoneNumber: z.object({
    countryCode: z.string().min(1),
    areaCode: z.string().min(1, 'Area code required').max(3),
    phoneNumber: z.string().min(6, 'Phone number required'),
  }),
  passengerIndex: z.number().optional(),
})

export function validateCompleteContact(values) {
  try {
    contactSchema.parse(values)
    return true
  } catch (e) {
    return false
  }
}

export function canPassengerBeContact(passenger) {
  if (!passenger) return false
  const type = String(passenger.type || '').toLowerCase()
  return type !== 'infant' && type !== 'ethnic_infant' && type !== 'labor_infant'
}

export function formatPhoneNumber(n) {
  return String(n || '')
}


